<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Filter System Example</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Hệ thống Filter Động - Example</h1>
        
        <!-- Filter Controls -->
        <div class="filter-controls">
            <!-- Price Filter (existing) -->
            <div class="filter-dropdown-container">
                <button class="filter-btn" id="priceBtn">
                    Mức giá
                    <span class="arrow">▼</span>
                </button>
                <div class="filter-dropdown" id="priceDropdown">
                    <!-- Price content remains the same -->
                </div>
            </div>

            <!-- Features Filter (existing with data-filter-type) -->
            <div class="filter-dropdown-container">
                <button class="filter-btn" id="featuresBtn">
                    Tính năng
                    <span class="arrow">▼</span>
                </button>
                <div class="filter-dropdown" id="featuresDropdown" data-filter-type="features">
                    <div class="dropdown-content">
                        <h4>Tính năng</h4>
                        <div class="checkbox-group">
                            <label><input type="checkbox" value="camera"> Camera chất lượng cao</label>
                            <label><input type="checkbox" value="battery"> Pin lâu</label>
                            <label><input type="checkbox" value="gaming"> Gaming</label>
                            <label><input type="checkbox" value="waterproof"> Chống nước</label>
                            <label><input type="checkbox" value="wireless"> Sạc không dây</label>
                        </div>
                        <div class="dropdown-footer">
                            <button class="btn-reset-small" onclick="resetFeaturesFilter()">Đặt lại</button>
                            <button class="btn-apply-small" onclick="applyFeaturesFilter()">Áp dụng</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Memory Filter (existing with data-filter-type) -->
            <div class="filter-dropdown-container">
                <button class="filter-btn" id="memoryBtn">
                    Bộ nhớ
                    <span class="arrow">▼</span>
                </button>
                <div class="filter-dropdown" id="memoryDropdown" data-filter-type="memory">
                    <div class="dropdown-content">
                        <h4>Bộ nhớ</h4>
                        <div class="checkbox-group">
                            <label><input type="checkbox" value="64gb"> 64GB</label>
                            <label><input type="checkbox" value="128gb"> 128GB</label>
                            <label><input type="checkbox" value="256gb"> 256GB</label>
                            <label><input type="checkbox" value="512gb"> 512GB</label>
                            <label><input type="checkbox" value="1tb"> 1TB</label>
                        </div>
                        <div class="dropdown-footer">
                            <button class="btn-reset-small" onclick="resetMemoryFilter()">Đặt lại</button>
                            <button class="btn-apply-small" onclick="applyMemoryFilter()">Áp dụng</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- NEW: Category Filter -->
            <div class="filter-dropdown-container">
                <button class="filter-btn" id="categoryBtn">
                    Thể loại
                    <span class="arrow">▼</span>
                </button>
                <div class="filter-dropdown" id="categoryDropdown" data-filter-type="category">
                    <div class="dropdown-content">
                        <h4>Thể loại</h4>
                        <div class="checkbox-group">
                            <label><input type="checkbox" value="smartphone"> Smartphone</label>
                            <label><input type="checkbox" value="tablet"> Tablet</label>
                            <label><input type="checkbox" value="laptop"> Laptop</label>
                            <label><input type="checkbox" value="smartwatch"> Smartwatch</label>
                            <label><input type="checkbox" value="headphone"> Tai nghe</label>
                        </div>
                        <div class="dropdown-footer">
                            <button class="btn-reset-small" onclick="resetCategoryFilter()">Đặt lại</button>
                            <button class="btn-apply-small" onclick="applyCategoryFilter()">Áp dụng</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- NEW: Usage Filter -->
            <div class="filter-dropdown-container">
                <button class="filter-btn" id="usageBtn">
                    Nhu cầu sử dụng
                    <span class="arrow">▼</span>
                </button>
                <div class="filter-dropdown" id="usageDropdown" data-filter-type="usage">
                    <div class="dropdown-content">
                        <h4>Nhu cầu sử dụng</h4>
                        <div class="checkbox-group">
                            <label><input type="checkbox" value="work"> Công việc</label>
                            <label><input type="checkbox" value="study"> Học tập</label>
                            <label><input type="checkbox" value="entertainment"> Giải trí</label>
                            <label><input type="checkbox" value="photography"> Chụp ảnh</label>
                            <label><input type="checkbox" value="business"> Kinh doanh</label>
                        </div>
                        <div class="dropdown-footer">
                            <button class="btn-reset-small" onclick="resetUsageFilter()">Đặt lại</button>
                            <button class="btn-apply-small" onclick="applyUsageFilter()">Áp dụng</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- NEW: Brand Filter -->
            <div class="filter-dropdown-container">
                <button class="filter-btn" id="brandBtn">
                    Thương hiệu
                    <span class="arrow">▼</span>
                </button>
                <div class="filter-dropdown" id="brandDropdown" data-filter-type="brand">
                    <div class="dropdown-content">
                        <h4>Thương hiệu</h4>
                        <div class="checkbox-group">
                            <label><input type="checkbox" value="apple"> Apple</label>
                            <label><input type="checkbox" value="samsung"> Samsung</label>
                            <label><input type="checkbox" value="xiaomi"> Xiaomi</label>
                            <label><input type="checkbox" value="oppo"> OPPO</label>
                            <label><input type="checkbox" value="vivo"> Vivo</label>
                        </div>
                        <div class="dropdown-footer">
                            <button class="btn-reset-small" onclick="resetBrandFilter()">Đặt lại</button>
                            <button class="btn-apply-small" onclick="applyBrandFilter()">Áp dụng</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Filter Button -->
            <button class="filter-btn main-filter" id="filterBtn">
                Bộ lọc
                <span class="arrow">▼</span>
            </button>
        </div>

        <!-- Enhanced Filter Popup with all filter types -->
        <div class="filter-popup" id="filterPopup">
            <div class="popup-content">
                <div class="popup-header">
                    <h3>Bộ lọc</h3>
                    <button class="close-btn" id="closeBtn">×</button>
                </div>

                <div class="popup-body">
                    <!-- Features Section -->
                    <div class="filter-section" data-filter-type="features">
                        <h4>Tính năng</h4>
                        <div class="tag-group">
                            <button class="tag-btn popup-tag" data-value="camera">Camera chất lượng cao</button>
                            <button class="tag-btn popup-tag" data-value="battery">Pin lâu</button>
                            <button class="tag-btn popup-tag" data-value="gaming">Gaming</button>
                            <button class="tag-btn popup-tag" data-value="waterproof">Chống nước</button>
                            <button class="tag-btn popup-tag" data-value="wireless">Sạc không dây</button>
                        </div>
                    </div>

                    <!-- Memory Section -->
                    <div class="filter-section" data-filter-type="memory">
                        <h4>Bộ nhớ</h4>
                        <div class="tag-group">
                            <button class="tag-btn popup-tag" data-value="64gb">64GB</button>
                            <button class="tag-btn popup-tag" data-value="128gb">128GB</button>
                            <button class="tag-btn popup-tag" data-value="256gb">256GB</button>
                            <button class="tag-btn popup-tag" data-value="512gb">512GB</button>
                            <button class="tag-btn popup-tag" data-value="1tb">1TB</button>
                        </div>
                    </div>

                    <!-- Category Section -->
                    <div class="filter-section" data-filter-type="category">
                        <h4>Thể loại</h4>
                        <div class="tag-group">
                            <button class="tag-btn popup-tag" data-value="smartphone">Smartphone</button>
                            <button class="tag-btn popup-tag" data-value="tablet">Tablet</button>
                            <button class="tag-btn popup-tag" data-value="laptop">Laptop</button>
                            <button class="tag-btn popup-tag" data-value="smartwatch">Smartwatch</button>
                            <button class="tag-btn popup-tag" data-value="headphone">Tai nghe</button>
                        </div>
                    </div>

                    <!-- Usage Section -->
                    <div class="filter-section" data-filter-type="usage">
                        <h4>Nhu cầu sử dụng</h4>
                        <div class="tag-group">
                            <button class="tag-btn popup-tag" data-value="work">Công việc</button>
                            <button class="tag-btn popup-tag" data-value="study">Học tập</button>
                            <button class="tag-btn popup-tag" data-value="entertainment">Giải trí</button>
                            <button class="tag-btn popup-tag" data-value="photography">Chụp ảnh</button>
                            <button class="tag-btn popup-tag" data-value="business">Kinh doanh</button>
                        </div>
                    </div>

                    <!-- Brand Section -->
                    <div class="filter-section" data-filter-type="brand">
                        <h4>Thương hiệu</h4>
                        <div class="tag-group">
                            <button class="tag-btn popup-tag" data-value="apple">Apple</button>
                            <button class="tag-btn popup-tag" data-value="samsung">Samsung</button>
                            <button class="tag-btn popup-tag" data-value="xiaomi">Xiaomi</button>
                            <button class="tag-btn popup-tag" data-value="oppo">OPPO</button>
                            <button class="tag-btn popup-tag" data-value="vivo">Vivo</button>
                        </div>
                    </div>
                </div>

                <div class="popup-footer">
                    <button class="btn-reset" onclick="resetAllFilters()">Đặt lại</button>
                    <button class="btn-apply" onclick="applyAllFilters()">Xem kết quả</button>
                </div>
            </div>
        </div>

        <!-- Overlay -->
        <div class="overlay" id="overlay"></div>

        <!-- Demo Results -->
        <div class="results-section">
            <h2>Kết quả Filter</h2>
            <div id="filterResults">
                <p>Chọn filter để xem kết quả...</p>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
