# 🚀 Hướng dẫn sử dụng Filter System cho nhiều trang

Hệ thống filter động cho phép sử dụng chung một bộ JavaScript cho nhiều trang khác nhau, mỗi trang có các loại filter riêng.

## ✨ Tính năng chính

- **🔄 Một script cho tất cả trang**: Không cần viết JS riêng cho từng trang
- **⚙️ Config-driven**: Mỗi trang chỉ cần config `window.FILTER_CONFIG`
- **🔄 Auto-sync**: Popup ↔ Dropdown tự động đồng bộ
- **📱 Mobile responsive**: Hoạt động tốt trên mọi thiết bị
- **🔧 Easy to extend**: Thêm filter mới chỉ cần config + HTML

## 📋 Cách sử dụng cho trang mới

### Bước 1: Config filter cho trang
```html
<!-- Đặt TRƯỚC khi load script.js -->
<script>
window.FILTER_CONFIG = {
    price: { 
        type: 'range', 
        defaultValue: { min: 0, max: 50000000 }, 
        label: 'Mức giá' 
    },
    features: { 
        type: 'multi-select', 
        defaultValue: [], 
        label: 'Tính năng' 
    },
    brand: { 
        type: 'multi-select', 
        defaultValue: [], 
        label: 'Thương hiệu' 
    }
    // Thêm filter khác tùy theo trang
};
</script>
```

### Bước 2: Load script chung
```html
<script src="script.js"></script>
```

### Bước 3: HTML dropdown với data-filter-type
```html
<div class="filter-dropdown" id="brandDropdown" data-filter-type="brand">
    <div class="dropdown-content">
        <h4>Thương hiệu</h4>
        <div class="checkbox-group">
            <label><input type="checkbox" value="apple"> Apple</label>
            <label><input type="checkbox" value="samsung"> Samsung</label>
            <label><input type="checkbox" value="xiaomi"> Xiaomi</label>
        </div>
        <div class="dropdown-footer">
            <button onclick="resetBrandFilter()">Đặt lại</button>
            <button onclick="applyBrandFilter()">Áp dụng</button>
        </div>
    </div>
</div>
```

### Bước 4: HTML popup với data-filter-type
```html
<div class="filter-section" data-filter-type="brand">
    <h4>Thương hiệu</h4>
    <div class="tag-group">
        <button class="tag-btn popup-tag">Apple</button>
        <button class="tag-btn popup-tag">Samsung</button>
        <button class="tag-btn popup-tag">Xiaomi</button>
    </div>
</div>
```

### Bước 5: Functions tự động có sẵn!
```javascript
// Tự động tạo cho mọi filter type:
window.applyBrandFilter();   // Áp dụng filter thương hiệu
window.resetBrandFilter();   // Reset filter thương hiệu

// Generic functions:
window.applyFilter('brand'); // Áp dụng bất kỳ filter nào
window.resetFilter('brand'); // Reset bất kỳ filter nào
```

## 📄 Examples cho các trang khác nhau

### 📱 Trang Smartphone
```javascript
window.FILTER_CONFIG = {
    price: { type: 'range', defaultValue: { min: 0, max: 50000000 }, label: 'Mức giá' },
    features: { type: 'multi-select', defaultValue: [], label: 'Tính năng' },
    memory: { type: 'multi-select', defaultValue: [], label: 'Bộ nhớ' },
    brand: { type: 'multi-select', defaultValue: [], label: 'Thương hiệu' }
};
```

### 💻 Trang Laptop
```javascript
window.FILTER_CONFIG = {
    price: { type: 'range', defaultValue: { min: 0, max: ********* }, label: 'Mức giá' },
    features: { type: 'multi-select', defaultValue: [], label: 'Tính năng' },
    usage: { type: 'multi-select', defaultValue: [], label: 'Nhu cầu sử dụng' },
    brand: { type: 'multi-select', defaultValue: [], label: 'Thương hiệu' },
    screen_size: { type: 'multi-select', defaultValue: [], label: 'Kích thước màn hình' }
};
```

### 🏠 Trang Bất động sản
```javascript
window.FILTER_CONFIG = {
    price: { type: 'range', defaultValue: { min: 0, max: 50000000000 }, label: 'Mức giá' },
    location: { type: 'multi-select', defaultValue: [], label: 'Khu vực' },
    property_type: { type: 'multi-select', defaultValue: [], label: 'Loại hình' },
    bedrooms: { type: 'multi-select', defaultValue: [], label: 'Số phòng ngủ' },
    amenities: { type: 'multi-select', defaultValue: [], label: 'Tiện ích' }
};
```

### 💼 Trang Tìm việc làm
```javascript
window.FILTER_CONFIG = {
    salary: { type: 'range', defaultValue: { min: 0, max: ********* }, label: 'Mức lương' },
    industry: { type: 'multi-select', defaultValue: [], label: 'Ngành nghề' },
    level: { type: 'multi-select', defaultValue: [], label: 'Cấp bậc' },
    location: { type: 'multi-select', defaultValue: [], label: 'Địa điểm' },
    company_size: { type: 'multi-select', defaultValue: [], label: 'Quy mô công ty' },
    skills: { type: 'multi-select', defaultValue: [], label: 'Kỹ năng' }
};
```

## 🔧 Các cách detect filter type

Hệ thống tự động detect filter type theo thứ tự:

### 1. data-filter-type attribute (Recommended)
```html
<div data-filter-type="brand">...</div>
```

### 2. ID pattern
```html
<div id="brandDropdown">...</div>
<!-- Tự động detect: brand -->
```

### 3. Class pattern
```html
<div class="brand-section">...</div>
<!-- Tự động detect: brand -->
```

### 4. Legacy support (filter-section index)
```html
<div class="filter-section">...</div> <!-- features -->
<div class="filter-section">...</div> <!-- memory -->
```

## ✅ Checklist cho trang mới

- [ ] Config `window.FILTER_CONFIG` trước khi load script
- [ ] HTML dropdown có `data-filter-type` hoặc ID pattern
- [ ] HTML popup có `data-filter-type` hoặc class pattern
- [ ] Checkbox trong dropdown có đúng text với tag trong popup
- [ ] Test sync: popup → dropdown và ngược lại
- [ ] Test apply/reset functions

## 🐛 Debug & Troubleshooting

### Vấn đề: Sync không hoạt động
```javascript
// Check console logs:
console.log('Detected filter type:', filterType);
console.log('Found X tag elements for selector:', selector);
console.log('Found Y checkboxes for selector:', selector);
```

### Vấn đề: Filter type không được detect
```javascript
// Check config:
console.log('FILTER_CONFIG:', window.FILTER_CONFIG);

// Check HTML attributes:
console.log('data-filter-type:', element.getAttribute('data-filter-type'));
```

### Vấn đề: Text không match
```javascript
// Check text content:
console.log('Tag text:', tag.textContent.trim());
console.log('Checkbox text:', checkbox.parentElement.textContent.trim());
```

## 🚀 Advanced Usage

### Custom filter types
```javascript
window.FILTER_CONFIG = {
    // Range filter
    price: { type: 'range', defaultValue: { min: 0, max: 1000000 } },
    
    // Multi-select filter
    tags: { type: 'multi-select', defaultValue: [] },
    
    // Có thể extend thêm types khác trong tương lai
    // single-select, date-range, etc.
};
```

### Multiple selectors
```html
<!-- Cả hai cách đều work -->
<div data-filter-type="brand" id="brandDropdown">...</div>
<div class="brand-section" data-filter="brand">...</div>
```

### Event hooks
```javascript
// Listen for filter changes
document.addEventListener('filterChanged', function(e) {
    console.log('Filter changed:', e.detail);
});
```

## 📞 Support

Nếu gặp vấn đề:
1. Check console logs để debug
2. Verify HTML structure và attributes
3. Test với config đơn giản trước
4. Kiểm tra text content có match chính xác không

---

**Happy filtering across multiple pages! 🎉**
