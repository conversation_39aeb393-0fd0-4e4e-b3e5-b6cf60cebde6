<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Filter System cho nhiều trang</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .page-demo {
            background: white;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .page-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .filter-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .filter-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
        .filter-btn:hover {
            background: #2980b9;
        }
        .filter-btn.main-filter {
            background: #e74c3c;
        }
        .filter-btn.main-filter:hover {
            background: #c0392b;
        }
        .config-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            margin-top: 15px;
        }
        .config-display h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        .config-display pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
        .instructions {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
            margin-bottom: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #27ae60;
        }
    </style>
</head>
<body>
    <h1>🚀 Demo - Hệ thống Filter cho nhiều trang</h1>
    
    <div class="instructions">
        <h3>📋 Hướng dẫn sử dụng:</h3>
        <p><strong>1.</strong> Mỗi trang chỉ cần config <code>window.FILTER_CONFIG</code> trước khi load script.js</p>
        <p><strong>2.</strong> HTML sử dụng <code>data-filter-type</code> hoặc ID pattern <code>#filterNameDropdown</code></p>
        <p><strong>3.</strong> Hệ thống tự động detect và sync giữa popup ↔ dropdown</p>
        <p><strong>4.</strong> Không cần viết JavaScript riêng cho từng filter!</p>
    </div>

    <!-- PAGE 1: Smartphone -->
    <div class="page-demo">
        <h2 class="page-title">📱 Trang 1: Smartphone</h2>
        
        <div class="filter-controls">
            <button class="filter-btn" onclick="alert('Features dropdown')">Tính năng</button>
            <button class="filter-btn" onclick="alert('Memory dropdown')">Bộ nhớ</button>
            <button class="filter-btn" onclick="alert('Brand dropdown')">Thương hiệu</button>
            <button class="filter-btn main-filter" onclick="alert('Main popup')">Bộ lọc</button>
        </div>

        <div class="config-display">
            <h4>Config cho trang Smartphone:</h4>
            <pre>window.FILTER_CONFIG = {
    price: { type: 'range', defaultValue: { min: 0, max: 50000000 }, label: 'Mức giá' },
    features: { type: 'multi-select', defaultValue: [], label: 'Tính năng' },
    memory: { type: 'multi-select', defaultValue: [], label: 'Bộ nhớ' },
    brand: { type: 'multi-select', defaultValue: [], label: 'Thương hiệu' }
};</pre>
        </div>
    </div>

    <!-- PAGE 2: Laptop -->
    <div class="page-demo">
        <h2 class="page-title">💻 Trang 2: Laptop</h2>
        
        <div class="filter-controls">
            <button class="filter-btn" onclick="alert('Features dropdown')">Tính năng</button>
            <button class="filter-btn" onclick="alert('Usage dropdown')">Nhu cầu sử dụng</button>
            <button class="filter-btn" onclick="alert('Brand dropdown')">Thương hiệu</button>
            <button class="filter-btn" onclick="alert('Screen dropdown')">Kích thước màn hình</button>
            <button class="filter-btn main-filter" onclick="alert('Main popup')">Bộ lọc</button>
        </div>

        <div class="config-display">
            <h4>Config cho trang Laptop:</h4>
            <pre>window.FILTER_CONFIG = {
    price: { type: 'range', defaultValue: { min: 0, max: ********* }, label: 'Mức giá' },
    features: { type: 'multi-select', defaultValue: [], label: 'Tính năng' },
    usage: { type: 'multi-select', defaultValue: [], label: 'Nhu cầu sử dụng' },
    brand: { type: 'multi-select', defaultValue: [], label: 'Thương hiệu' },
    screen_size: { type: 'multi-select', defaultValue: [], label: 'Kích thước màn hình' }
};</pre>
        </div>
    </div>

    <!-- PAGE 3: Real Estate -->
    <div class="page-demo">
        <h2 class="page-title">🏠 Trang 3: Bất động sản</h2>
        
        <div class="filter-controls">
            <button class="filter-btn" onclick="alert('Location dropdown')">Khu vực</button>
            <button class="filter-btn" onclick="alert('Type dropdown')">Loại hình</button>
            <button class="filter-btn" onclick="alert('Bedrooms dropdown')">Số phòng ngủ</button>
            <button class="filter-btn" onclick="alert('Amenities dropdown')">Tiện ích</button>
            <button class="filter-btn main-filter" onclick="alert('Main popup')">Bộ lọc</button>
        </div>

        <div class="config-display">
            <h4>Config cho trang Bất động sản:</h4>
            <pre>window.FILTER_CONFIG = {
    price: { type: 'range', defaultValue: { min: 0, max: 50000000000 }, label: 'Mức giá' },
    location: { type: 'multi-select', defaultValue: [], label: 'Khu vực' },
    property_type: { type: 'multi-select', defaultValue: [], label: 'Loại hình' },
    bedrooms: { type: 'multi-select', defaultValue: [], label: 'Số phòng ngủ' },
    amenities: { type: 'multi-select', defaultValue: [], label: 'Tiện ích' }
};</pre>
        </div>
    </div>

    <!-- PAGE 4: Job Search -->
    <div class="page-demo">
        <h2 class="page-title">💼 Trang 4: Tìm việc làm</h2>
        
        <div class="filter-controls">
            <button class="filter-btn" onclick="alert('Industry dropdown')">Ngành nghề</button>
            <button class="filter-btn" onclick="alert('Level dropdown')">Cấp bậc</button>
            <button class="filter-btn" onclick="alert('Location dropdown')">Địa điểm</button>
            <button class="filter-btn" onclick="alert('Company dropdown')">Công ty</button>
            <button class="filter-btn" onclick="alert('Skills dropdown')">Kỹ năng</button>
            <button class="filter-btn main-filter" onclick="alert('Main popup')">Bộ lọc</button>
        </div>

        <div class="config-display">
            <h4>Config cho trang Tìm việc:</h4>
            <pre>window.FILTER_CONFIG = {
    salary: { type: 'range', defaultValue: { min: 0, max: ********* }, label: 'Mức lương' },
    industry: { type: 'multi-select', defaultValue: [], label: 'Ngành nghề' },
    level: { type: 'multi-select', defaultValue: [], label: 'Cấp bậc' },
    location: { type: 'multi-select', defaultValue: [], label: 'Địa điểm' },
    company_size: { type: 'multi-select', defaultValue: [], label: 'Quy mô công ty' },
    skills: { type: 'multi-select', defaultValue: [], label: 'Kỹ năng' }
};</pre>
        </div>
    </div>

    <div class="config-display">
        <h4>🔧 Cách implement cho trang mới:</h4>
        <pre>&lt;!-- 1. Config trước khi load script --&gt;
&lt;script&gt;
window.FILTER_CONFIG = {
    price: { type: 'range', defaultValue: { min: 0, max: 50000000 }, label: 'Mức giá' },
    features: { type: 'multi-select', defaultValue: [], label: 'Tính năng' },
    brand: { type: 'multi-select', defaultValue: [], label: 'Thương hiệu' }
    // Thêm filter khác tùy theo trang
};
&lt;/script&gt;

&lt;!-- 2. Load script chung --&gt;
&lt;script src="script.js"&gt;&lt;/script&gt;

&lt;!-- 3. HTML dropdown với data-filter-type --&gt;
&lt;div class="filter-dropdown" id="brandDropdown" data-filter-type="brand"&gt;
    &lt;div class="checkbox-group"&gt;
        &lt;label&gt;&lt;input type="checkbox" value="apple"&gt; Apple&lt;/label&gt;
        &lt;label&gt;&lt;input type="checkbox" value="samsung"&gt; Samsung&lt;/label&gt;
    &lt;/div&gt;
    &lt;button onclick="applyBrandFilter()"&gt;Áp dụng&lt;/button&gt;
&lt;/div&gt;

&lt;!-- 4. HTML popup với data-filter-type --&gt;
&lt;div class="filter-section" data-filter-type="brand"&gt;
    &lt;h4&gt;Thương hiệu&lt;/h4&gt;
    &lt;div class="tag-group"&gt;
        &lt;button class="tag-btn popup-tag"&gt;Apple&lt;/button&gt;
        &lt;button class="tag-btn popup-tag"&gt;Samsung&lt;/button&gt;
    &lt;/div&gt;
&lt;/div&gt;</pre>
    </div>

    <div class="config-display">
        <h4>✨ Tính năng tự động có sẵn:</h4>
        <pre>// Functions tự động tạo cho mọi filter type:
window.applyBrandFilter();     // Áp dụng filter thương hiệu
window.resetBrandFilter();     // Reset filter thương hiệu
window.applyIndustryFilter();  // Áp dụng filter ngành nghề
window.resetIndustryFilter();  // Reset filter ngành nghề

// Generic functions:
window.applyFilter('brand');   // Áp dụng bất kỳ filter nào
window.resetFilter('brand');   // Reset bất kỳ filter nào

// Auto-sync:
// Click tag trong popup → checkbox trong dropdown tự động checked
// Check checkbox trong dropdown → tag trong popup tự động active</pre>
    </div>

    <script>
        // Demo config cho trang hiện tại
        window.FILTER_CONFIG = {
            price: { type: 'range', defaultValue: { min: 0, max: 47990000 }, label: 'Mức giá' },
            features: { type: 'multi-select', defaultValue: [], label: 'Tính năng' },
            memory: { type: 'multi-select', defaultValue: [], label: 'Bộ nhớ' },
            brand: { type: 'multi-select', defaultValue: [], label: 'Thương hiệu' }
        };
        
        console.log('Demo page loaded with config:', window.FILTER_CONFIG);
    </script>
</body>
</html>
