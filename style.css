* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background-color: #f5f5f5;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Filter Bar */
.filter-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  border-color: #ff6b6b;
  color: #ff6b6b;
}

.main-filter {
  background: #ff6b6b;
  color: white;
  border-color: #ff6b6b;
}

.main-filter:hover {
  background: #ff5252;
  color: white;
}

.filter-icon {
  font-size: 16px;
}

.arrow {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.desktop-filters {
  display: flex;
  gap: 10px;
}

/* Filter Dropdown Container */
.filter-dropdown-container {
  position: relative;
}

/* Filter Dropdown */
.filter-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 280px;
  display: none;
  margin-top: 4px;
}

.filter-dropdown.active {
  display: block;
}

.dropdown-content {
  padding: 16px;
}

.dropdown-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.dropdown-footer {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.btn-reset-small,
.btn-apply-small {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-reset-small {
  background: #f8f9fa;
  color: #666;
}

.btn-reset-small:hover {
  background: #e9ecef;
}

.btn-apply-small {
  background: #ff6b6b;
  color: white;
}

.btn-apply-small:hover {
  background: #ff5252;
}

.sort-options {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.sort-btn:hover {
  background: #f8f9fa;
}

.sort-icon {
  font-size: 14px;
}

/* Filter Popup */
.filter-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  z-index: 1001;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  display: none;
}

.popup-content {
  padding: 0;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.popup-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: #f0f0f0;
}

.popup-body {
  padding: 24px;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

/* Price Range Slider */
.price-range {
  position: relative;
}

.range-slider {
  position: relative;
  height: 40px;
  margin-bottom: 16px;
}

.range-slider input[type="range"] {
  position: absolute;
  width: 100%;
  height: 6px;
  background: none;
  pointer-events: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.range-slider input[type="range"]::-webkit-slider-track {
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
}

.range-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #ff6b6b;
  cursor: pointer;
  pointer-events: all;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin-top: -7px; /* Move thumb up to sit on top of track */
}

.range-slider input[type="range"]::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #ff6b6b;
  cursor: pointer;
  pointer-events: all;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin-top: -7px; /* Move thumb up to sit on top of track */
}

.price-display {
  text-align: center;
  font-weight: 600;
  color: #ff6b6b;
  font-size: 14px;
}

/* Checkbox Group */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #ff6b6b;
}

/* Popup Footer */
.popup-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e0e0e0;
}

.btn-reset,
.btn-apply {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-reset {
  background: #f8f9fa;
  color: #666;
}

.btn-reset:hover {
  background: #e9ecef;
}

.btn-apply {
  background: #ff6b6b;
  color: white;
}

.btn-apply:hover {
  background: #ff5252;
}

/* Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .filter-bar {
    padding: 12px 16px;
    flex-wrap: wrap;
  }

  .desktop-filters {
    display: none;
  }

  .main-filter {
    order: 2;
    margin-left: auto;
  }

  .sort-options {
    order: 1;
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }

  .filter-popup {
    width: 95%;
    max-height: 90vh;
  }

  .popup-body {
    padding: 20px;
  }

  .popup-footer {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .sort-options {
    flex-direction: column;
    gap: 8px;
  }

  .sort-btn {
    justify-content: center;
  }
}
