* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background-color: #f5f5f5;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Filter Bar */
.filter-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  border-color: #ff6b6b;
  color: #ff6b6b;
}

.main-filter {
  background: #ff6b6b;
  color: white;
  border-color: #ff6b6b;
}

.main-filter:hover {
  background: #ff5252;
  color: white;
}

.filter-icon {
  font-size: 16px;
}

.arrow {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.desktop-filters {
  display: flex;
  gap: 10px;
}

/* Filter Dropdown Container */
.filter-dropdown-container {
  position: relative;
}

/* Filter Dropdown */
.filter-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 280px;
  display: none;
  margin-top: 4px;
}

/* Mobile dropdown adjustments */
@media (max-width: 768px) {
  .filter-dropdown,
  .mobile-price-dropdown {
    position: absolute;
    top: 100%;
    left: -16px;
    right: -16px;
    width: calc(100vw - 32px);
    min-width: auto;
    max-height: 60vh;
    overflow-y: auto;
    z-index: 9999;
    margin-top: 8px;
    border-radius: 12px;
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .filter-dropdown.show,
  .mobile-price-dropdown.show {
    transform: translateY(0);
    opacity: 1;
  }
}

.filter-dropdown.active {
  display: block;
}

.dropdown-content {
  padding: 16px;
}

.dropdown-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.dropdown-footer {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.btn-reset-small,
.btn-apply-small {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-reset-small {
  background: #f8f9fa;
  color: #666;
}

.btn-reset-small:hover {
  background: #e9ecef;
}

.btn-apply-small {
  background: #ff6b6b;
  color: white;
}

.btn-apply-small:hover {
  background: #ff5252;
}

/* Price Input Range */
.price-input-range {
  margin-bottom: 16px;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.price-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
  background: white;
  color: #333;
  outline: none;
  transition: border-color 0.2s ease;
}

.price-input:focus {
  border-color: #ff6b6b;
}

.price-input::placeholder {
  color: #999;
}

.price-separator {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

/* Custom Visual Slider */
.price-slider-visual {
  position: relative;
  height: 6px;
  margin: 20px 0;
}

.slider-track-bg {
  position: absolute;
  width: 100%;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
}

.slider-track-active {
  position: absolute;
  height: 6px;
  background: #ff6b6b;
  border-radius: 3px;
  left: 0%;
  width: 100%;
}

.slider-thumb {
  position: absolute;
  width: 20px;
  height: 20px;
  background: #ff6b6b;
  border: 2px solid white;
  border-radius: 50%;
  top: -7px;
  cursor: grab;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
  user-select: none;
  z-index: 10;
}

.slider-thumb:hover {
  transform: scale(1.1);
}

.slider-thumb:active {
  cursor: grabbing;
  transform: scale(1.2);
}

.slider-thumb:first-of-type {
  left: 0%;
  margin-left: -10px; /* Center the thumb on its position */
}

.slider-thumb:last-of-type {
  left: 100%;
  margin-left: -10px; /* Center the thumb on its position */
}

/* Make slider container more interactive */
.price-slider-visual {
  cursor: pointer;
  padding: 10px 0; /* Add padding for easier clicking */
}

.sort-options {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

/* Hide mobile price container on desktop */
.mobile-price-container {
  display: none;
  position: relative;
}

.mobile-price-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.mobile-price-btn:hover {
  border-color: #ff6b6b;
  color: #ff6b6b;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.sort-btn:hover {
  background: #f8f9fa;
}

.sort-icon {
  font-size: 14px;
}

/* Filter Popup */
.filter-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  z-index: 1001;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  display: none;
}

.popup-content {
  padding: 0;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.popup-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: #f0f0f0;
}

.popup-body {
  padding: 24px;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

/* Price Range Slider */
.price-range {
  position: relative;
}

.range-slider {
  position: relative;
  height: 40px;
  margin-bottom: 16px;
}

.range-slider input[type="range"] {
  position: absolute;
  width: 100%;
  height: 6px;
  background: none;
  pointer-events: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  top: 17px; /* Center the input vertically */
}

.range-slider input[type="range"]::-webkit-slider-track {
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
}

.range-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #ff6b6b;
  cursor: pointer;
  pointer-events: all;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin-top: -10px; /* Move thumb up to sit on track */
}

.range-slider input[type="range"]::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #ff6b6b;
  cursor: pointer;
  pointer-events: all;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  margin-top: -10px; /* Move thumb up to sit on track */
  border: none; /* Remove default border for Firefox */
}

.price-display {
  text-align: center;
  font-weight: 600;
  color: #ff6b6b;
  font-size: 14px;
}

/* Checkbox Group */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #ff6b6b;
}

/* Popup Footer */
.popup-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e0e0e0;
}

.btn-reset,
.btn-apply {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-reset {
  background: #f8f9fa;
  color: #666;
}

.btn-reset:hover {
  background: #e9ecef;
}

.btn-apply {
  background: #ff6b6b;
  color: white;
}

.btn-apply:hover {
  background: #ff5252;
}

/* Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.overlay.show {
  opacity: 1;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .filter-bar {
    padding: 12px 16px;
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 8px;
    position: relative;
    z-index: 10;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
  }

  .desktop-filters {
    display: none;
  }

  .main-filter {
    order: 4;
    flex-shrink: 0;
  }

  .sort-options {
    order: 2;
    margin-left: 0;
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  }

  /* Show price container for mobile */
  .mobile-price-container {
    order: 1;
    display: block !important;
    position: relative;
    flex-shrink: 0;
    width: auto;
  }

  /* Mobile dropdown specific styling */
  .mobile-price-dropdown .dropdown-content {
    padding: 16px;
  }

  .mobile-price-dropdown .price-inputs {
    gap: 8px;
  }

  .mobile-price-dropdown .price-input {
    min-width: 0;
    flex: 1;
  }

  .filter-popup {
    width: 100%;
    max-height: 85vh;
    position: fixed;
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 16px 16px 0 0;
    margin: 0;
  }

  .filter-popup.show {
    transform: translateY(0);
  }

  .popup-body {
    padding: 20px;
  }

  .popup-footer {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .filter-bar {
    gap: 6px;
  }

  .sort-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .mobile-price-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .main-filter {
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* Tag Group */
.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.tag-btn {
  padding: 10px 18px;
  border: 2px solid #e8e8e8;
  border-radius: 25px;
  background: #ffffff;
  color: #555;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  position: relative;
  overflow: hidden;
  user-select: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tag-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 107, 107, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.tag-btn:hover::before {
  left: 100%;
}

.tag-btn:hover {
  border-color: #ff6b6b;
  color: #ff6b6b;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.2);
}

.tag-btn.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
  border-color: #ff6b6b;
  color: white;
  box-shadow: 0 6px 16px rgba(255, 107, 107, 0.3);
}

.tag-btn.active:hover {
  background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%);
  border-color: #ff5252;
  box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
}

/* Special styling for different tag types */
.tag-btn[data-value*="sim"],
.tag-btn[data-value*="4g"] {
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border-color: #e3e8ff;
  color: #4c51bf;
}

.tag-btn[data-value*="sim"]:hover,
.tag-btn[data-value*="4g"]:hover {
  border-color: #4c51bf;
  color: #4c51bf;
  box-shadow: 0 4px 12px rgba(76, 81, 191, 0.2);
}

.tag-btn[data-value*="sim"].active,
.tag-btn[data-value*="4g"].active {
  background: linear-gradient(135deg, #4c51bf 0%, #3730a3 100%);
  border-color: #4c51bf;
  color: white;
}

/* Memory tags special styling */
.tag-btn[data-value*="gb"],
.tag-btn[data-value*="tb"] {
  background: linear-gradient(135deg, #f0fff4 0%, #ffffff 100%);
  border-color: #c6f6d5;
  color: #2d7d32;
  font-weight: 600;
}

.tag-btn[data-value*="gb"]:hover,
.tag-btn[data-value*="tb"]:hover {
  border-color: #2d7d32;
  color: #2d7d32;
  box-shadow: 0 4px 12px rgba(45, 125, 50, 0.2);
}

.tag-btn[data-value*="gb"].active,
.tag-btn[data-value*="tb"].active {
  background: linear-gradient(135deg, #2d7d32 0%, #1b5e20 100%);
  border-color: #2d7d32;
  color: white;
}
