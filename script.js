document.addEventListener('DOMContentLoaded', function () {
    // Elements
    const filterBtn = document.getElementById('filterBtn');
    const filterPopup = document.getElementById('filterPopup');
    const overlay = document.getElementById('overlay');
    const closeBtn = document.getElementById('closeBtn');
    const resetBtn = document.getElementById('resetBtn');
    const applyBtn = document.getElementById('applyBtn');

    // Desktop dropdown elements
    const priceBtn = document.getElementById('priceBtn');
    const featuresBtn = document.getElementById('featuresBtn');
    const memoryBtn = document.getElementById('memoryBtn');
    const priceDropdown = document.getElementById('priceDropdown');
    const featuresDropdown = document.getElementById('featuresDropdown');
    const memoryDropdown = document.getElementById('memoryDropdown');

    // Price range elements (popup)
    const minPriceSlider = document.getElementById('minPrice');
    const maxPriceSlider = document.getElementById('maxPrice');
    const minPriceDisplay = document.getElementById('minPriceDisplay');
    const maxPriceDisplay = document.getElementById('maxPriceDisplay');

    // Price input elements (desktop dropdown)
    const minPriceInput = document.getElementById('minPriceInput');
    const maxPriceInput = document.getElementById('maxPriceInput');
    const sliderTrackActive = document.getElementById('sliderTrackActive');
    const minThumb = document.getElementById('minThumb');
    const maxThumb = document.getElementById('maxThumb');

    // Mobile price elements
    const minPriceMobile = document.getElementById('minPriceMobile');
    const maxPriceMobile = document.getElementById('maxPriceMobile');
    const sliderTrackActiveMobile = document.getElementById('sliderTrackActiveMobile');
    const minThumbMobile = document.getElementById('minThumbMobile');
    const maxThumbMobile = document.getElementById('maxThumbMobile');
    const mobilePriceDropdown = document.getElementById('mobilePriceDropdown');

    // Check if mobile
    function isMobile() {
        return window.innerWidth <= 768;
    }

    // Open popup
    function openPopup() {
        filterPopup.style.display = 'block';
        overlay.style.display = 'block';
        document.body.style.overflow = 'hidden';

        if (isMobile()) {
            // Mobile animation
            setTimeout(() => {
                filterPopup.classList.add('show');
                overlay.classList.add('show');
            }, 10);
        } else {
            // Desktop - no animation needed
            overlay.classList.add('show');
        }
    }

    // Close popup
    function closePopup() {
        if (isMobile()) {
            // Mobile animation
            filterPopup.classList.remove('show');
            overlay.classList.remove('show');

            setTimeout(() => {
                filterPopup.style.display = 'none';
                overlay.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        } else {
            // Desktop - immediate close
            filterPopup.style.display = 'none';
            overlay.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    }

    // Dropdown functions
    function toggleDropdown(dropdown) {
        // Close all other dropdowns
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => {
            if (dd !== dropdown) {
                closeDropdown(dd);
            }
        });

        // Toggle current dropdown
        if (dropdown.classList.contains('active')) {
            closeDropdown(dropdown);
        } else {
            openDropdown(dropdown);
        }
    }

    function openDropdown(dropdown) {
        console.log('Opening dropdown:', dropdown.id, 'isMobile:', isMobile());
        dropdown.classList.add('active');
        dropdown.style.display = 'block';

        if (isMobile()) {
            setTimeout(() => {
                dropdown.classList.add('show');
                console.log('Added show class to:', dropdown.id);
            }, 10);
        }
    }

    function closeDropdown(dropdown) {
        if (isMobile()) {
            dropdown.classList.remove('show');
            setTimeout(() => {
                dropdown.classList.remove('active');
                dropdown.style.display = 'none';
            }, 300);
        } else {
            dropdown.classList.remove('active');
            dropdown.style.display = 'none';
        }
    }

    function closeAllDropdowns() {
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => closeDropdown(dd));
    }

    // Format price display
    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN').format(price) + 'đ';
    }

    // Update price display (popup)
    function updatePriceDisplay() {
        const minPrice = parseInt(minPriceSlider.value);
        const maxPrice = parseInt(maxPriceSlider.value);

        // Ensure min is not greater than max
        if (minPrice > maxPrice) {
            minPriceSlider.value = maxPrice;
        }
        if (maxPrice < minPrice) {
            maxPriceSlider.value = minPrice;
        }

        minPriceDisplay.textContent = formatPrice(minPriceSlider.value);
        maxPriceDisplay.textContent = formatPrice(maxPriceSlider.value);

        // Update slider track color
        updateSliderTrack();
    }

    // Format number with dots
    function formatNumberWithDots(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }

    // Parse price from input (remove dots and 'đ')
    function parsePriceInput(value) {
        return parseInt(value.replace(/\./g, '').replace('đ', '')) || 0;
    }

    // Update price inputs and visual slider
    function updatePriceInputs() {
        const minValue = parsePriceInput(minPriceInput.value);
        const maxValue = parsePriceInput(maxPriceInput.value);

        // Update visual slider
        updateVisualSlider(minValue, maxValue);
    }

    // Update mobile price inputs and visual slider
    function updateMobilePriceInputs() {
        if (!minPriceMobile || !maxPriceMobile) return;

        const minValue = parsePriceInput(minPriceMobile.value);
        const maxValue = parsePriceInput(maxPriceMobile.value);

        // Update mobile visual slider
        updateMobileVisualSlider(minValue, maxValue);
    }

    // Update visual slider position
    function updateVisualSlider(minValue, maxValue) {
        if (!sliderTrackActive || !minThumb || !maxThumb) return;

        const maxRange = 50000000; // 50 million
        const minPercent = Math.max(0, Math.min(100, (minValue / maxRange) * 100));
        const maxPercent = Math.max(0, Math.min(100, (maxValue / maxRange) * 100));

        // Update active track
        sliderTrackActive.style.left = minPercent + '%';
        sliderTrackActive.style.width = (maxPercent - minPercent) + '%';

        // Update thumbs
        minThumb.style.left = minPercent + '%';
        maxThumb.style.left = maxPercent + '%';
    }

    // Update mobile visual slider position
    function updateMobileVisualSlider(minValue, maxValue) {
        if (!sliderTrackActiveMobile || !minThumbMobile || !maxThumbMobile) return;

        const maxRange = 50000000; // 50 million
        const minPercent = Math.max(0, Math.min(100, (minValue / maxRange) * 100));
        const maxPercent = Math.max(0, Math.min(100, (maxValue / maxRange) * 100));

        // Update active track
        sliderTrackActiveMobile.style.left = minPercent + '%';
        sliderTrackActiveMobile.style.width = (maxPercent - minPercent) + '%';

        // Update thumbs
        minThumbMobile.style.left = minPercent + '%';
        maxThumbMobile.style.left = maxPercent + '%';
    }

    // Draggable slider functionality
    let isDragging = false;
    let currentThumb = null;
    const maxRange = 50000000;
    const stepSize = 1000; // Step by 1,000 VND

    function initDraggableSlider() {
        const sliderContainer = document.querySelector('#priceDropdown .price-slider-visual');

        // Mouse events for thumbs
        minThumb.addEventListener('mousedown', function (e) {
            isDragging = true;
            currentThumb = 'min';
            e.preventDefault();
        });

        maxThumb.addEventListener('mousedown', function (e) {
            isDragging = true;
            currentThumb = 'max';
            e.preventDefault();
        });

        // Click on track to move nearest thumb
        sliderContainer.addEventListener('click', function (e) {
            if (e.target === minThumb || e.target === maxThumb) return;

            const rect = sliderContainer.getBoundingClientRect();
            const clickPercent = ((e.clientX - rect.left) / rect.width) * 100;
            const clickValue = (clickPercent / 100) * maxRange;
            const roundedValue = Math.round(clickValue / stepSize) * stepSize; // Round to nearest thousand

            const minValue = parsePriceInput(minPriceInput.value);
            const maxValue = parsePriceInput(maxPriceInput.value);

            // Move the nearest thumb
            if (Math.abs(roundedValue - minValue) < Math.abs(roundedValue - maxValue)) {
                minPriceInput.value = formatNumberWithDots(roundedValue) + 'đ';
            } else {
                maxPriceInput.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updatePriceInputs();
        });

        // Mouse move event
        document.addEventListener('mousemove', function (e) {
            if (!isDragging) return;

            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize; // Round to nearest thousand

            if (currentThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceInput.value);
                if (value <= maxValue) {
                    minPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentThumb === 'max') {
                const minValue = parsePriceInput(minPriceInput.value);
                if (value >= minValue) {
                    maxPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updatePriceInputs();
        });

        // Mouse up event
        document.addEventListener('mouseup', function () {
            isDragging = false;
            currentThumb = null;
        });

        // Touch events for mobile
        minThumb.addEventListener('touchstart', function (e) {
            isDragging = true;
            currentThumb = 'min';
            e.preventDefault();
        });

        maxThumb.addEventListener('touchstart', function (e) {
            isDragging = true;
            currentThumb = 'max';
            e.preventDefault();
        });

        document.addEventListener('touchmove', function (e) {
            if (!isDragging) return;

            const touch = e.touches[0];
            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize; // Round to nearest thousand

            if (currentThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceInput.value);
                if (value <= maxValue) {
                    minPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentThumb === 'max') {
                const minValue = parsePriceInput(minPriceInput.value);
                if (value >= minValue) {
                    maxPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updatePriceInputs();
            e.preventDefault();
        });

        document.addEventListener('touchend', function () {
            isDragging = false;
            currentThumb = null;
        });
    }

    // Mobile draggable slider functionality
    function initMobileDraggableSlider() {
        if (!minThumbMobile || !maxThumbMobile || !sliderTrackActiveMobile) return;

        const sliderContainer = document.querySelector('#mobilePriceDropdown .price-slider-visual');
        if (!sliderContainer) return;

        let isMobileDragging = false;
        let currentMobileThumb = null;

        // Mouse events for mobile thumbs
        minThumbMobile.addEventListener('mousedown', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'min';
            e.preventDefault();
        });

        maxThumbMobile.addEventListener('mousedown', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'max';
            e.preventDefault();
        });

        // Click on mobile track to move nearest thumb
        sliderContainer.addEventListener('click', function (e) {
            if (e.target === minThumbMobile || e.target === maxThumbMobile) return;

            const rect = sliderContainer.getBoundingClientRect();
            const clickPercent = ((e.clientX - rect.left) / rect.width) * 100;
            const clickValue = (clickPercent / 100) * maxRange;
            const roundedValue = Math.round(clickValue / stepSize) * stepSize;

            const minValue = parsePriceInput(minPriceMobile.value);
            const maxValue = parsePriceInput(maxPriceMobile.value);

            // Move the nearest thumb
            if (Math.abs(roundedValue - minValue) < Math.abs(roundedValue - maxValue)) {
                minPriceMobile.value = formatNumberWithDots(roundedValue) + 'đ';
            } else {
                maxPriceMobile.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updateMobilePriceInputs();
        });

        // Mouse move event for mobile
        document.addEventListener('mousemove', function (e) {
            if (!isMobileDragging) return;

            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize;

            if (currentMobileThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceMobile.value);
                if (value <= maxValue) {
                    minPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentMobileThumb === 'max') {
                const minValue = parsePriceInput(minPriceMobile.value);
                if (value >= minValue) {
                    maxPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updateMobilePriceInputs();
        });

        // Mouse up event for mobile
        document.addEventListener('mouseup', function () {
            isMobileDragging = false;
            currentMobileThumb = null;
        });

        // Touch events for mobile
        minThumbMobile.addEventListener('touchstart', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'min';
            e.preventDefault();
        });

        maxThumbMobile.addEventListener('touchstart', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'max';
            e.preventDefault();
        });

        document.addEventListener('touchmove', function (e) {
            if (!isMobileDragging) return;

            const touch = e.touches[0];
            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize;

            if (currentMobileThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceMobile.value);
                if (value <= maxValue) {
                    minPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentMobileThumb === 'max') {
                const minValue = parsePriceInput(minPriceMobile.value);
                if (value >= minValue) {
                    maxPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updateMobilePriceInputs();
            e.preventDefault();
        });

        document.addEventListener('touchend', function () {
            isMobileDragging = false;
            currentMobileThumb = null;
        });
    }

    // Update slider track visual (popup)
    function updateSliderTrack() {
        const minVal = parseInt(minPriceSlider.value);
        const maxVal = parseInt(maxPriceSlider.value);
        const minRange = parseInt(minPriceSlider.min);
        const maxRange = parseInt(minPriceSlider.max);

        const minPercent = ((minVal - minRange) / (maxRange - minRange)) * 100;
        const maxPercent = ((maxVal - minRange) / (maxRange - minRange)) * 100;

        // Create visual track effect
        const rangeSlider = document.querySelector('.popup-body .range-slider');
        let track = rangeSlider.querySelector('.slider-track');

        if (!track) {
            track = document.createElement('div');
            track.className = 'slider-track';
            track.style.position = 'absolute';
            track.style.height = '6px';
            track.style.background = '#ff6b6b';
            track.style.borderRadius = '3px';
            track.style.top = '17px';
            track.style.zIndex = '1';
            rangeSlider.appendChild(track);
        }

        track.style.left = minPercent + '%';
        track.style.width = (maxPercent - minPercent) + '%';
    }

    // Update slider track visual (desktop dropdown)
    function updateSliderTrackDesktop() {
        const minVal = parseInt(minPriceDesktopSlider.value);
        const maxVal = parseInt(maxPriceDesktopSlider.value);
        const minRange = parseInt(minPriceDesktopSlider.min);
        const maxRange = parseInt(minPriceDesktopSlider.max);

        const minPercent = ((minVal - minRange) / (maxRange - minRange)) * 100;
        const maxPercent = ((maxVal - minRange) / (maxRange - minRange)) * 100;

        // Create visual track effect
        const rangeSlider = document.querySelector('#priceDropdown .range-slider');
        let track = rangeSlider.querySelector('.slider-track');

        if (!track) {
            track = document.createElement('div');
            track.className = 'slider-track';
            track.style.position = 'absolute';
            track.style.height = '6px';
            track.style.background = '#ff6b6b';
            track.style.borderRadius = '3px';
            track.style.top = '17px';
            track.style.zIndex = '1';
            rangeSlider.appendChild(track);
        }

        track.style.left = minPercent + '%';
        track.style.width = (maxPercent - minPercent) + '%';
    }

    // Reset all filters
    function resetFilters() {
        // Reset price sliders (only if they exist)
        if (minPriceSlider && maxPriceSlider) {
            minPriceSlider.value = minPriceSlider.min;
            maxPriceSlider.value = maxPriceSlider.max;
            updatePriceDisplay();
        }

        // Reset all popup tag buttons
        const popupTags = document.querySelectorAll('.popup-tag');
        popupTags.forEach(tag => {
            tag.classList.remove('active');
        });
    }

    // Apply filters
    function applyFilters() {
        const filters = {
            features: [],
            memory: []
        };

        // Get selected features from popup
        const featureTags = document.querySelectorAll('.filter-section:nth-child(1) .popup-tag.active');
        featureTags.forEach(tag => {
            filters.features.push(tag.textContent);
        });

        // Get selected memory options from popup
        const memoryTags = document.querySelectorAll('.filter-section:nth-child(2) .popup-tag.active');
        memoryTags.forEach(tag => {
            filters.memory.push(tag.textContent);
        });

        console.log('Applied filters:', filters);

        // Here you would typically send the filters to your backend or update the product display
        // For demo purposes, we'll just show an alert
        let filterSummary = `Bộ lọc đã áp dụng:\n`;

        if (filters.features.length > 0) {
            filterSummary += `Tính năng: ${filters.features.join(', ')}\n`;
        }

        if (filters.memory.length > 0) {
            filterSummary += `Bộ nhớ: ${filters.memory.join(', ')}\n`;
        }

        if (filters.features.length === 0 && filters.memory.length === 0) {
            filterSummary += `Không có bộ lọc nào được chọn`;
        }

        alert(filterSummary);
        closePopup();
    }

    // Event listeners
    filterBtn.addEventListener('click', function (e) {
        e.stopPropagation(); // Prevent the click from triggering the outside click handler
        openPopup();
    });
    closeBtn.addEventListener('click', closePopup);
    overlay.addEventListener('click', closePopup);
    resetBtn.addEventListener('click', resetFilters);
    applyBtn.addEventListener('click', applyFilters);

    // Prevent popup from closing when clicking inside popup content
    if (filterPopup) {
        filterPopup.addEventListener('click', function (e) {
            e.stopPropagation();
        });
    }

    // Price slider event listeners (only if elements exist)
    if (minPriceSlider && maxPriceSlider) {
        minPriceSlider.addEventListener('input', updatePriceDisplay);
        maxPriceSlider.addEventListener('input', updatePriceDisplay);
    }

    // Close popup with Escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            if (filterPopup.style.display === 'block') {
                closePopup();
            }
            closeAllDropdowns();
        }
    });

    // Desktop dropdown filter functions
    window.resetPriceFilter = function () {
        if (minPriceInput && maxPriceInput) {
            minPriceInput.value = '0.000đ';
            maxPriceInput.value = '47.990.000đ';
            updatePriceInputs();
        }
        // Don't close dropdown, just reset
    }

    window.applyPriceFilter = function () {
        const minPrice = parsePriceInput(minPriceInput.value);
        const maxPrice = parsePriceInput(maxPriceInput.value);
        console.log('Applied price filter:', { minPrice, maxPrice });
        alert(`Đã áp dụng bộ lọc giá: ${formatPrice(minPrice)} - ${formatPrice(maxPrice)}`);
        closeAllDropdowns();
    }

    window.resetFeaturesFilter = function () {
        const tagBtns = document.querySelectorAll('#featuresDropdown .tag-btn');
        tagBtns.forEach(btn => btn.classList.remove('active'));
        // Don't close dropdown, just reset
    }

    window.applyFeaturesFilter = function () {
        const activeTags = document.querySelectorAll('#featuresDropdown .tag-btn.active');
        const features = Array.from(activeTags).map(btn => btn.textContent);
        console.log('Applied features filter:', features);
        alert(`Đã áp dụng bộ lọc tính năng: ${features.join(', ')}`);
        closeAllDropdowns();
    }

    window.resetMemoryFilter = function () {
        const tagBtns = document.querySelectorAll('#memoryDropdown .tag-btn');
        tagBtns.forEach(btn => btn.classList.remove('active'));
        // Don't close dropdown, just reset
    }

    window.applyMemoryFilter = function () {
        const activeTags = document.querySelectorAll('#memoryDropdown .tag-btn.active');
        const memory = Array.from(activeTags).map(btn => btn.textContent);
        console.log('Applied memory filter:', memory);
        alert(`Đã áp dụng bộ lọc bộ nhớ: ${memory.join(', ')}`);
        closeAllDropdowns();
    }

    // Mobile price filter functions
    window.resetMobilePriceFilter = function () {
        if (minPriceMobile && maxPriceMobile) {
            minPriceMobile.value = '0.000đ';
            maxPriceMobile.value = '47.990.000đ';
        }
        closeAllDropdowns();
    }

    window.applyMobilePriceFilter = function () {
        if (minPriceMobile && maxPriceMobile) {
            const minPrice = parsePriceInput(minPriceMobile.value);
            const maxPrice = parsePriceInput(maxPriceMobile.value);
            console.log('Applied mobile price filter:', { minPrice, maxPrice });
            alert(`Đã áp dụng bộ lọc giá: ${formatPrice(minPrice)} - ${formatPrice(maxPrice)}`);
        }
        closeAllDropdowns();
    }

    // Initialize price displays (only if elements exist)
    if (minPriceSlider && maxPriceSlider) {
        updatePriceDisplay();
    }

    // Initialize price inputs with default values
    if (minPriceInput && maxPriceInput) {
        minPriceInput.value = '0.000đ';
        maxPriceInput.value = '47.990.000đ';
        updatePriceInputs();

        // Initialize draggable slider
        initDraggableSlider();
    }

    // Initialize mobile price inputs
    if (minPriceMobile && maxPriceMobile) {
        minPriceMobile.value = '0.000đ';
        maxPriceMobile.value = '47.990.000đ';

        // Initialize mobile visual slider
        updateMobilePriceInputs();
        initMobileDraggableSlider();

        // Mobile price input event listeners
        minPriceMobile.addEventListener('input', function () {
            let value = this.value.replace(/\D/g, '');
            if (value) {
                const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
                this.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updateMobilePriceInputs();
        });

        maxPriceMobile.addEventListener('input', function () {
            let value = this.value.replace(/\D/g, '');
            if (value) {
                const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
                this.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updateMobilePriceInputs();
        });
    }

    // Desktop dropdown event listeners
    priceBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(priceDropdown);
    });

    featuresBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(featuresDropdown);
    });

    memoryBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(memoryDropdown);
    });

    // Mobile price button event listener
    const mobilePriceBtn = document.getElementById('mobilePriceBtn');
    if (mobilePriceBtn && mobilePriceDropdown) {
        mobilePriceBtn.addEventListener('click', function (e) {
            e.stopPropagation();
            console.log('Mobile price button clicked, isMobile:', isMobile());
            console.log('mobilePriceDropdown:', mobilePriceDropdown);

            if (!isMobile()) {
                // Desktop behavior - position dropdown below filter bar
                const filterBar = document.querySelector('.filter-bar');
                const rect = filterBar.getBoundingClientRect();
                mobilePriceDropdown.style.top = (rect.bottom + 8) + 'px';
            }

            toggleDropdown(mobilePriceDropdown);
        });
    } else {
        console.log('Mobile price button or dropdown not found:', {
            mobilePriceBtn: !!mobilePriceBtn,
            mobilePriceDropdown: !!mobilePriceDropdown
        });
    }

    // Price input event listeners
    minPriceInput.addEventListener('input', function () {
        // Format input as user types
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        if (value) {
            // Round to nearest thousand
            const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
            this.value = formatNumberWithDots(roundedValue) + 'đ';
        }
        updatePriceInputs();
    });

    maxPriceInput.addEventListener('input', function () {
        // Format input as user types
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        if (value) {
            // Round to nearest thousand
            const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
            this.value = formatNumberWithDots(roundedValue) + 'đ';
        }
        updatePriceInputs();
    });

    // Tag button event listeners (for both popup and dropdown)
    document.addEventListener('click', function (e) {
        if (e.target.classList.contains('tag-btn')) {
            e.target.classList.toggle('active');
            e.stopPropagation(); // Prevent dropdown from closing
        }
    });

    // Prevent dropdowns from closing when clicking inside them
    document.querySelectorAll('.filter-dropdown').forEach(dropdown => {
        dropdown.addEventListener('click', function (e) {
            e.stopPropagation();
        });
    });

    // Close dropdowns when clicking outside, but not when clicking inside
    document.addEventListener('click', function (e) {
        // Don't close if clicking inside any dropdown or popup
        if (e.target.closest('.filter-dropdown') ||
            e.target.closest('.filter-popup') ||
            e.target.closest('.filter-dropdown-container') ||
            e.target.closest('.mobile-price-container') ||
            e.target.closest('.filter-btn') ||
            e.target.closest('.mobile-price-btn')) {
            return;
        }

        // Close all dropdowns if clicking outside
        closeAllDropdowns();

        // Close popup if clicking outside
        if (filterPopup.style.display === 'block') {
            closePopup();
        }
    });

    // Add hover effects for desktop filter buttons
    const desktopFilterBtns = document.querySelectorAll('.desktop-filters .filter-btn');
    desktopFilterBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(180deg)';
            }
        });

        btn.addEventListener('mouseleave', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0deg)';
            }
        });
    });
});
