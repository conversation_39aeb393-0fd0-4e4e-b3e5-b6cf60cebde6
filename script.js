document.addEventListener('DOMContentLoaded', function () {
    // Elements
    const filterBtn = document.getElementById('filterBtn');
    const filterPopup = document.getElementById('filterPopup');
    const overlay = document.getElementById('overlay');
    const closeBtn = document.getElementById('closeBtn');
    const resetBtn = document.getElementById('resetBtn');
    const applyBtn = document.getElementById('applyBtn');

    // Desktop dropdown elements
    const priceBtn = document.getElementById('priceBtn');
    const featuresBtn = document.getElementById('featuresBtn');
    const memoryBtn = document.getElementById('memoryBtn');
    const priceDropdown = document.getElementById('priceDropdown');
    const featuresDropdown = document.getElementById('featuresDropdown');
    const memoryDropdown = document.getElementById('memoryDropdown');

    // Price range elements (popup)
    const minPriceSlider = document.getElementById('minPrice');
    const maxPriceSlider = document.getElementById('maxPrice');
    const minPriceDisplay = document.getElementById('minPriceDisplay');
    const maxPriceDisplay = document.getElementById('maxPriceDisplay');

    // Price input elements (desktop dropdown)
    const minPriceInput = document.getElementById('minPriceInput');
    const maxPriceInput = document.getElementById('maxPriceInput');
    const sliderTrackActive = document.getElementById('sliderTrackActive');
    const minThumb = document.getElementById('minThumb');
    const maxThumb = document.getElementById('maxThumb');

    // Open popup
    function openPopup() {
        filterPopup.style.display = 'block';
        overlay.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    // Close popup
    function closePopup() {
        filterPopup.style.display = 'none';
        overlay.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // Dropdown functions
    function toggleDropdown(dropdown) {
        // Close all other dropdowns
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => {
            if (dd !== dropdown) {
                dd.classList.remove('active');
            }
        });

        // Toggle current dropdown
        dropdown.classList.toggle('active');
    }

    function closeAllDropdowns() {
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => dd.classList.remove('active'));
    }

    // Format price display
    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN').format(price) + 'đ';
    }

    // Update price display (popup)
    function updatePriceDisplay() {
        const minPrice = parseInt(minPriceSlider.value);
        const maxPrice = parseInt(maxPriceSlider.value);

        // Ensure min is not greater than max
        if (minPrice > maxPrice) {
            minPriceSlider.value = maxPrice;
        }
        if (maxPrice < minPrice) {
            maxPriceSlider.value = minPrice;
        }

        minPriceDisplay.textContent = formatPrice(minPriceSlider.value);
        maxPriceDisplay.textContent = formatPrice(maxPriceSlider.value);

        // Update slider track color
        updateSliderTrack();
    }

    // Format number with dots
    function formatNumberWithDots(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }

    // Parse price from input (remove dots and 'đ')
    function parsePriceInput(value) {
        return parseInt(value.replace(/\./g, '').replace('đ', '')) || 0;
    }

    // Update price inputs and visual slider
    function updatePriceInputs() {
        const minValue = parsePriceInput(minPriceInput.value);
        const maxValue = parsePriceInput(maxPriceInput.value);

        // Update visual slider
        updateVisualSlider(minValue, maxValue);
    }

    // Update visual slider position
    function updateVisualSlider(minValue, maxValue) {
        const maxRange = 50000000; // 50 million
        const minPercent = Math.max(0, Math.min(100, (minValue / maxRange) * 100));
        const maxPercent = Math.max(0, Math.min(100, (maxValue / maxRange) * 100));

        // Update active track
        sliderTrackActive.style.left = minPercent + '%';
        sliderTrackActive.style.width = (maxPercent - minPercent) + '%';

        // Update thumbs
        minThumb.style.left = minPercent + '%';
        maxThumb.style.left = maxPercent + '%';
    }

    // Draggable slider functionality
    let isDragging = false;
    let currentThumb = null;
    const maxRange = 50000000;

    function initDraggableSlider() {
        const sliderContainer = document.querySelector('#priceDropdown .price-slider-visual');

        // Mouse events for thumbs
        minThumb.addEventListener('mousedown', function (e) {
            isDragging = true;
            currentThumb = 'min';
            e.preventDefault();
        });

        maxThumb.addEventListener('mousedown', function (e) {
            isDragging = true;
            currentThumb = 'max';
            e.preventDefault();
        });

        // Click on track to move nearest thumb
        sliderContainer.addEventListener('click', function (e) {
            if (e.target === minThumb || e.target === maxThumb) return;

            const rect = sliderContainer.getBoundingClientRect();
            const clickPercent = ((e.clientX - rect.left) / rect.width) * 100;
            const clickValue = (clickPercent / 100) * maxRange;

            const minValue = parsePriceInput(minPriceInput.value);
            const maxValue = parsePriceInput(maxPriceInput.value);

            // Move the nearest thumb
            if (Math.abs(clickValue - minValue) < Math.abs(clickValue - maxValue)) {
                minPriceInput.value = formatNumberWithDots(Math.round(clickValue)) + 'đ';
            } else {
                maxPriceInput.value = formatNumberWithDots(Math.round(clickValue)) + 'đ';
            }
            updatePriceInputs();
        });

        // Mouse move event
        document.addEventListener('mousemove', function (e) {
            if (!isDragging) return;

            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange);

            if (currentThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceInput.value);
                if (value <= maxValue) {
                    minPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentThumb === 'max') {
                const minValue = parsePriceInput(minPriceInput.value);
                if (value >= minValue) {
                    maxPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updatePriceInputs();
        });

        // Mouse up event
        document.addEventListener('mouseup', function () {
            isDragging = false;
            currentThumb = null;
        });

        // Touch events for mobile
        minThumb.addEventListener('touchstart', function (e) {
            isDragging = true;
            currentThumb = 'min';
            e.preventDefault();
        });

        maxThumb.addEventListener('touchstart', function (e) {
            isDragging = true;
            currentThumb = 'max';
            e.preventDefault();
        });

        document.addEventListener('touchmove', function (e) {
            if (!isDragging) return;

            const touch = e.touches[0];
            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange);

            if (currentThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceInput.value);
                if (value <= maxValue) {
                    minPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentThumb === 'max') {
                const minValue = parsePriceInput(minPriceInput.value);
                if (value >= minValue) {
                    maxPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updatePriceInputs();
            e.preventDefault();
        });

        document.addEventListener('touchend', function () {
            isDragging = false;
            currentThumb = null;
        });
    }

    // Update slider track visual (popup)
    function updateSliderTrack() {
        const minVal = parseInt(minPriceSlider.value);
        const maxVal = parseInt(maxPriceSlider.value);
        const minRange = parseInt(minPriceSlider.min);
        const maxRange = parseInt(minPriceSlider.max);

        const minPercent = ((minVal - minRange) / (maxRange - minRange)) * 100;
        const maxPercent = ((maxVal - minRange) / (maxRange - minRange)) * 100;

        // Create visual track effect
        const rangeSlider = document.querySelector('.popup-body .range-slider');
        let track = rangeSlider.querySelector('.slider-track');

        if (!track) {
            track = document.createElement('div');
            track.className = 'slider-track';
            track.style.position = 'absolute';
            track.style.height = '6px';
            track.style.background = '#ff6b6b';
            track.style.borderRadius = '3px';
            track.style.top = '17px';
            track.style.zIndex = '1';
            rangeSlider.appendChild(track);
        }

        track.style.left = minPercent + '%';
        track.style.width = (maxPercent - minPercent) + '%';
    }

    // Update slider track visual (desktop dropdown)
    function updateSliderTrackDesktop() {
        const minVal = parseInt(minPriceDesktopSlider.value);
        const maxVal = parseInt(maxPriceDesktopSlider.value);
        const minRange = parseInt(minPriceDesktopSlider.min);
        const maxRange = parseInt(minPriceDesktopSlider.max);

        const minPercent = ((minVal - minRange) / (maxRange - minRange)) * 100;
        const maxPercent = ((maxVal - minRange) / (maxRange - minRange)) * 100;

        // Create visual track effect
        const rangeSlider = document.querySelector('#priceDropdown .range-slider');
        let track = rangeSlider.querySelector('.slider-track');

        if (!track) {
            track = document.createElement('div');
            track.className = 'slider-track';
            track.style.position = 'absolute';
            track.style.height = '6px';
            track.style.background = '#ff6b6b';
            track.style.borderRadius = '3px';
            track.style.top = '17px';
            track.style.zIndex = '1';
            rangeSlider.appendChild(track);
        }

        track.style.left = minPercent + '%';
        track.style.width = (maxPercent - minPercent) + '%';
    }

    // Reset all filters
    function resetFilters() {
        // Reset price sliders
        minPriceSlider.value = minPriceSlider.min;
        maxPriceSlider.value = maxPriceSlider.max;
        updatePriceDisplay();

        // Reset all checkboxes
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    // Apply filters
    function applyFilters() {
        const filters = {
            minPrice: parseInt(minPriceSlider.value),
            maxPrice: parseInt(maxPriceSlider.value),
            features: [],
            memory: []
        };

        // Get selected features
        const featureCheckboxes = document.querySelectorAll('.filter-section:nth-child(2) input[type="checkbox"]:checked');
        featureCheckboxes.forEach(checkbox => {
            filters.features.push(checkbox.value);
        });

        // Get selected memory options
        const memoryCheckboxes = document.querySelectorAll('.filter-section:nth-child(3) input[type="checkbox"]:checked');
        memoryCheckboxes.forEach(checkbox => {
            filters.memory.push(checkbox.value);
        });

        console.log('Applied filters:', filters);

        // Here you would typically send the filters to your backend or update the product display
        // For demo purposes, we'll just show an alert
        let filterSummary = `Bộ lọc đã áp dụng:\n`;
        filterSummary += `Giá: ${formatPrice(filters.minPrice)} - ${formatPrice(filters.maxPrice)}\n`;

        if (filters.features.length > 0) {
            filterSummary += `Tính năng: ${filters.features.join(', ')}\n`;
        }

        if (filters.memory.length > 0) {
            filterSummary += `Bộ nhớ: ${filters.memory.join(', ')}\n`;
        }

        alert(filterSummary);
        closePopup();
    }

    // Event listeners
    filterBtn.addEventListener('click', openPopup);
    closeBtn.addEventListener('click', closePopup);
    overlay.addEventListener('click', closePopup);
    resetBtn.addEventListener('click', resetFilters);
    applyBtn.addEventListener('click', applyFilters);

    // Price slider event listeners
    minPriceSlider.addEventListener('input', updatePriceDisplay);
    maxPriceSlider.addEventListener('input', updatePriceDisplay);

    // Close popup with Escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape' && filterPopup.style.display === 'block') {
            closePopup();
        }
    });

    // Desktop dropdown filter functions
    window.resetPriceFilter = function () {
        minPriceInput.value = '0.000đ';
        maxPriceInput.value = '47.990.000đ';
        updatePriceInputs();
        closeAllDropdowns();
    }

    window.applyPriceFilter = function () {
        const minPrice = parsePriceInput(minPriceInput.value);
        const maxPrice = parsePriceInput(maxPriceInput.value);
        console.log('Applied price filter:', { minPrice, maxPrice });
        alert(`Đã áp dụng bộ lọc giá: ${formatPrice(minPrice)} - ${formatPrice(maxPrice)}`);
        closeAllDropdowns();
    }

    window.resetFeaturesFilter = function () {
        const checkboxes = document.querySelectorAll('#featuresDropdown input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
    }

    window.applyFeaturesFilter = function () {
        const checkboxes = document.querySelectorAll('#featuresDropdown input[type="checkbox"]:checked');
        const features = Array.from(checkboxes).map(cb => cb.value);
        console.log('Applied features filter:', features);
        alert(`Đã áp dụng bộ lọc tính năng: ${features.join(', ')}`);
        closeAllDropdowns();
    }

    window.resetMemoryFilter = function () {
        const checkboxes = document.querySelectorAll('#memoryDropdown input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
    }

    window.applyMemoryFilter = function () {
        const checkboxes = document.querySelectorAll('#memoryDropdown input[type="checkbox"]:checked');
        const memory = Array.from(checkboxes).map(cb => cb.value);
        console.log('Applied memory filter:', memory);
        alert(`Đã áp dụng bộ lọc bộ nhớ: ${memory.join(', ')}`);
        closeAllDropdowns();
    }

    // Initialize price displays
    updatePriceDisplay();

    // Initialize price inputs with default values
    minPriceInput.value = '0.000đ';
    maxPriceInput.value = '47.990.000đ';
    updatePriceInputs();

    // Initialize draggable slider
    initDraggableSlider();

    // Desktop dropdown event listeners
    priceBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(priceDropdown);
    });

    featuresBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(featuresDropdown);
    });

    memoryBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(memoryDropdown);
    });

    // Price input event listeners
    minPriceInput.addEventListener('input', function () {
        // Format input as user types
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        if (value) {
            this.value = formatNumberWithDots(value) + 'đ';
        }
        updatePriceInputs();
    });

    maxPriceInput.addEventListener('input', function () {
        // Format input as user types
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        if (value) {
            this.value = formatNumberWithDots(value) + 'đ';
        }
        updatePriceInputs();
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function (e) {
        if (!e.target.closest('.filter-dropdown-container')) {
            closeAllDropdowns();
        }
    });

    // Add hover effects for desktop filter buttons
    const desktopFilterBtns = document.querySelectorAll('.desktop-filters .filter-btn');
    desktopFilterBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(180deg)';
            }
        });

        btn.addEventListener('mouseleave', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0deg)';
            }
        });
    });
});
