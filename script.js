document.addEventListener('DOMContentLoaded', function () {
    // Elements
    const filterBtn = document.getElementById('filterBtn');
    const filterPopup = document.getElementById('filterPopup');
    const overlay = document.getElementById('overlay');
    const closeBtn = document.getElementById('closeBtn');
    const resetBtn = document.getElementById('resetBtn');
    const applyBtn = document.getElementById('applyBtn');

    // Desktop dropdown elements
    const priceBtn = document.getElementById('priceBtn');
    const featuresBtn = document.getElementById('featuresBtn');
    const memoryBtn = document.getElementById('memoryBtn');
    const priceDropdown = document.getElementById('priceDropdown');
    const featuresDropdown = document.getElementById('featuresDropdown');
    const memoryDropdown = document.getElementById('memoryDropdown');

    // Price range elements (popup)
    const minPriceSlider = document.getElementById('minPrice');
    const maxPriceSlider = document.getElementById('maxPrice');
    const minPriceDisplay = document.getElementById('minPriceDisplay');
    const maxPriceDisplay = document.getElementById('maxPriceDisplay');

    // Price input elements (desktop dropdown)
    const minPriceInput = document.getElementById('minPriceInput');
    const maxPriceInput = document.getElementById('maxPriceInput');
    const sliderTrackActive = document.getElementById('sliderTrackActive');
    const minThumb = document.getElementById('minThumb');
    const maxThumb = document.getElementById('maxThumb');

    // Mobile price elements
    const minPriceMobile = document.getElementById('minPriceMobile');
    const maxPriceMobile = document.getElementById('maxPriceMobile');
    const sliderTrackActiveMobile = document.getElementById('sliderTrackActiveMobile');
    const minThumbMobile = document.getElementById('minThumbMobile');
    const maxThumbMobile = document.getElementById('maxThumbMobile');
    const mobilePriceDropdown = document.getElementById('mobilePriceDropdown');

    // Dynamic filter configuration
    const FILTER_CONFIG = {
        price: {
            type: 'range',
            defaultValue: { min: 0, max: 47990000 }
        },
        features: {
            type: 'multi-select',
            defaultValue: []
        },
        memory: {
            type: 'multi-select',
            defaultValue: []
        },
        category: {
            type: 'multi-select',
            defaultValue: []
        },
        usage: {
            type: 'multi-select',
            defaultValue: []
        },
        brand: {
            type: 'multi-select',
            defaultValue: []
        }
        // Có thể thêm nhiều filter khác: color, storage, screen_size, etc.
    };

    // Initialize dynamic filter state
    function initializeFilterState() {
        const state = {};
        Object.keys(FILTER_CONFIG).forEach(key => {
            const config = FILTER_CONFIG[key];
            if (config.type === 'range') {
                state[key] = { ...config.defaultValue };
            } else if (config.type === 'multi-select') {
                state[key] = [...config.defaultValue];
            }
        });
        return state;
    }

    // Global filter state - dynamically initialized
    let filterState = initializeFilterState();
    let savedState = initializeFilterState(); // Backup state when opening dropdown/popup

    let justAppliedFilter = false; // Flag to prevent restore after apply

    // Check if mobile
    function isMobile() {
        return window.innerWidth <= 768;
    }

    // Save current filter state
    function saveCurrentState() {
        // Ensure filterState is valid before saving
        if (!filterState || typeof filterState !== 'object') {
            console.warn('Invalid filterState, initializing with defaults');
            filterState = {
                price: { min: 0, max: 47990000 },
                features: [],
                memory: []
            };
        }

        savedState = {
            price: filterState.price ? { ...filterState.price } : { min: 0, max: 47990000 },
            features: Array.isArray(filterState.features) ? [...filterState.features] : [],
            memory: Array.isArray(filterState.memory) ? [...filterState.memory] : []
        };
        console.log('Saved state:', savedState);
    }

    // Restore saved state
    function restoreSavedState() {
        // Validate savedState before restoring
        if (!savedState || typeof savedState !== 'object') {
            console.warn('Invalid savedState, using default state');
            return;
        }

        filterState = {
            price: savedState.price ? { ...savedState.price } : { min: 0, max: 47990000 },
            features: Array.isArray(savedState.features) ? [...savedState.features] : [],
            memory: Array.isArray(savedState.memory) ? [...savedState.memory] : []
        };

        // Update UI to reflect restored state
        updateUIFromState();
        console.log('Restored state:', filterState);
    }

    // Update UI elements based on current state
    function updateUIFromState() {
        // Validate filterState before updating UI
        if (!filterState || typeof filterState !== 'object') {
            console.warn('Invalid filterState in updateUIFromState');
            return;
        }

        // Update price inputs
        console.log('=== UPDATE UI FROM STATE - PRICE ===');
        console.log('filterState.price:', filterState.price);
        console.log('minPriceInput exists:', !!minPriceInput);
        console.log('maxPriceInput exists:', !!maxPriceInput);
        console.log('minPriceMobile exists:', !!minPriceMobile);
        console.log('maxPriceMobile exists:', !!maxPriceMobile);

        if (minPriceInput && maxPriceInput && filterState.price) {
            const minValue = formatPrice(filterState.price.min || 0);
            const maxValue = formatPrice(filterState.price.max || 47990000);

            console.log('Setting desktop inputs to:', minValue, maxValue);

            minPriceInput.value = minValue;
            maxPriceInput.value = maxValue;

            console.log('Desktop inputs after setting:', minPriceInput.value, maxPriceInput.value);

            if (typeof updatePriceInputs === 'function') {
                updatePriceInputs();
                console.log('Called updatePriceInputs()');
            }
        }

        if (minPriceMobile && maxPriceMobile && filterState.price) {
            const minValue = formatPrice(filterState.price.min || 0);
            const maxValue = formatPrice(filterState.price.max || 47990000);

            console.log('Setting mobile inputs to:', minValue, maxValue);

            minPriceMobile.value = minValue;
            maxPriceMobile.value = maxValue;

            console.log('Mobile inputs after setting:', minPriceMobile.value, maxPriceMobile.value);

            if (typeof updateMobilePriceInputs === 'function') {
                updateMobilePriceInputs();
                console.log('Called updateMobilePriceInputs()');
            }
        }

        // Update all multi-select filters dynamically
        Object.keys(FILTER_CONFIG).forEach(filterKey => {
            const config = FILTER_CONFIG[filterKey];

            if (config.type === 'multi-select' && Array.isArray(filterState[filterKey])) {
                // Update popup tags - use data-filter-type or fallback selectors
                const tagSelectors = [
                    `[data-filter-type="${filterKey}"] .popup-tag`,
                    `[data-filter-type="${filterKey}"] .tag-btn`,
                    `.${filterKey}-section .popup-tag`,
                    `.${filterKey}-section .tag-btn`
                ];

                // Legacy support for filter-section index
                if (filterKey === 'features') {
                    tagSelectors.push('.filter-section:nth-child(1) .popup-tag');
                } else if (filterKey === 'memory') {
                    tagSelectors.push('.filter-section:nth-child(2) .popup-tag');
                }

                tagSelectors.forEach(selector => {
                    document.querySelectorAll(selector).forEach(btn => {
                        const isActive = filterState[filterKey].includes(btn.textContent.trim());
                        btn.classList.toggle('active', isActive);
                    });
                });

                // Update dropdown checkboxes - use data-filter-type or fallback selectors
                const checkboxSelectors = [
                    `[data-filter-type="${filterKey}"] input[type="checkbox"]`,
                    `#${filterKey}Dropdown input[type="checkbox"]`
                ];

                checkboxSelectors.forEach(selector => {
                    document.querySelectorAll(selector).forEach(checkbox => {
                        const isChecked = filterState[filterKey].includes(checkbox.parentElement.textContent.trim());
                        checkbox.checked = isChecked;
                    });
                });

                console.log(`Synced ${filterKey} across all locations:`, filterState[filterKey]);
            }
        });
    }

    // Send filters to API
    async function sendFiltersToAPI(filters) {
        try {
            console.log('Sending filters to API:', filters);

            // Replace with your actual API endpoint
            const response = await fetch('/api/products/filter', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(filters)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('API response:', data);

            // Handle successful response
            handleAPIResponse(data);
            return data;

        } catch (error) {
            console.error('API Error:', error);

            // For demo purposes, simulate API response
            const mockData = {
                products: [
                    { id: 1, name: 'iPhone 15 Pro', price: 25000000 },
                    { id: 2, name: 'Samsung Galaxy S24', price: 20000000 }
                ],
                total: 2,
                filters: filters
            };

            setTimeout(() => {
                handleAPIResponse(mockData);
            }, 500);

            return mockData;
        }
    }

    // Handle API response
    function handleAPIResponse(data) {
        console.log('Processing API response:', data);

        // Update UI with filtered products
        updateFilterResults(data);
        alert(`Tìm thấy ${data.total} sản phẩm phù hợp với bộ lọc!`);
    }

    // Open popup
    function openPopup() {
        saveCurrentState(); // Save state before opening

        filterPopup.style.display = 'block';
        overlay.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // Update popup UI to reflect current state
        updateUIFromState();

        if (isMobile()) {
            // Mobile animation
            setTimeout(() => {
                filterPopup.classList.add('show');
                overlay.classList.add('show');
            }, 10);
        } else {
            // Desktop - no animation needed
            overlay.classList.add('show');
        }
    }

    // Close popup (restore state if not applied)
    function closePopup(applied = false) {
        if (!applied) {
            restoreSavedState(); // Restore previous state if not applied
        }

        if (isMobile()) {
            // Mobile animation
            filterPopup.classList.remove('show');
            overlay.classList.remove('show');

            setTimeout(() => {
                filterPopup.style.display = 'none';
                overlay.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        } else {
            // Desktop - immediate close
            filterPopup.style.display = 'none';
            overlay.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    }

    // Dropdown functions
    function toggleDropdown(dropdown) {
        // Close all other dropdowns
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => {
            if (dd !== dropdown) {
                closeDropdown(dd);
            }
        });

        // Toggle current dropdown
        if (dropdown.classList.contains('active')) {
            closeDropdown(dropdown);
        } else {
            openDropdown(dropdown);
        }
    }

    function openDropdown(dropdown) {
        console.log('=== OPEN DROPDOWN START ===');
        console.log('Dropdown:', dropdown.id);
        console.log('Current filterState before save:', JSON.stringify(filterState));

        saveCurrentState(); // Save state before opening dropdown

        console.log('Saved state:', JSON.stringify(savedState));

        dropdown.classList.add('active');
        dropdown.style.display = 'block';

        // Update UI to reflect current state when opening
        console.log('About to updateUIFromState...');
        updateUIFromState();

        console.log('After updateUIFromState:');
        console.log('Desktop inputs:', minPriceInput?.value, maxPriceInput?.value);
        console.log('Mobile inputs:', minPriceMobile?.value, maxPriceMobile?.value);

        if (isMobile()) {
            setTimeout(() => {
                dropdown.classList.add('show');
            }, 10);
        }

        console.log('=== OPEN DROPDOWN END ===');
    }

    function closeDropdown(dropdown, applied = false) {
        console.log('Closing dropdown:', dropdown.id, 'applied:', applied);

        if (!applied) {
            console.log('Restoring saved state because not applied');
            restoreSavedState(); // Restore previous state if not applied
        } else {
            console.log('Not restoring state because applied = true');
        }

        if (isMobile()) {
            dropdown.classList.remove('show');
            setTimeout(() => {
                dropdown.classList.remove('active');
                dropdown.style.display = 'none';
            }, 300);
        } else {
            dropdown.classList.remove('active');
            dropdown.style.display = 'none';
        }
    }

    function closeAllDropdowns(applied = false) {
        console.log('closeAllDropdowns called with applied:', applied);
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => closeDropdown(dd, applied));
    }

    // Format price display
    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN').format(price) + 'đ';
    }

    // Update price display (popup)
    function updatePriceDisplay() {
        const minPrice = parseInt(minPriceSlider.value);
        const maxPrice = parseInt(maxPriceSlider.value);

        // Ensure min is not greater than max
        if (minPrice > maxPrice) {
            minPriceSlider.value = maxPrice;
        }
        if (maxPrice < minPrice) {
            maxPriceSlider.value = minPrice;
        }

        minPriceDisplay.textContent = formatPrice(minPriceSlider.value);
        maxPriceDisplay.textContent = formatPrice(maxPriceSlider.value);

        // Update slider track color
        updateSliderTrack();
    }

    // Format number with dots
    function formatNumberWithDots(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }

    // Parse price from input (remove dots and 'đ')
    function parsePriceInput(value) {
        return parseInt(value.replace(/\./g, '').replace('đ', '')) || 0;
    }

    // Update price inputs and visual slider
    function updatePriceInputs() {
        const minValue = parsePriceInput(minPriceInput.value);
        const maxValue = parsePriceInput(maxPriceInput.value);

        // Update visual slider
        updateVisualSlider(minValue, maxValue);
    }

    // Update mobile price inputs and visual slider
    function updateMobilePriceInputs() {
        if (!minPriceMobile || !maxPriceMobile) return;

        const minValue = parsePriceInput(minPriceMobile.value);
        const maxValue = parsePriceInput(maxPriceMobile.value);

        // Update mobile visual slider
        updateMobileVisualSlider(minValue, maxValue);
    }

    // Update visual slider position
    function updateVisualSlider(minValue, maxValue) {
        if (!sliderTrackActive || !minThumb || !maxThumb) return;

        const maxRange = 50000000; // 50 million
        const minPercent = Math.max(0, Math.min(100, (minValue / maxRange) * 100));
        const maxPercent = Math.max(0, Math.min(100, (maxValue / maxRange) * 100));

        // Update active track
        sliderTrackActive.style.left = minPercent + '%';
        sliderTrackActive.style.width = (maxPercent - minPercent) + '%';

        // Update thumbs
        minThumb.style.left = minPercent + '%';
        maxThumb.style.left = maxPercent + '%';
    }

    // Update mobile visual slider position
    function updateMobileVisualSlider(minValue, maxValue) {
        if (!sliderTrackActiveMobile || !minThumbMobile || !maxThumbMobile) return;

        const maxRange = 50000000; // 50 million
        const minPercent = Math.max(0, Math.min(100, (minValue / maxRange) * 100));
        const maxPercent = Math.max(0, Math.min(100, (maxValue / maxRange) * 100));

        // Update active track
        sliderTrackActiveMobile.style.left = minPercent + '%';
        sliderTrackActiveMobile.style.width = (maxPercent - minPercent) + '%';

        // Update thumbs
        minThumbMobile.style.left = minPercent + '%';
        maxThumbMobile.style.left = maxPercent + '%';
    }

    // Draggable slider functionality
    let isDragging = false;
    let currentThumb = null;
    const maxRange = 50000000;
    const stepSize = 1000; // Step by 1,000 VND

    function initDraggableSlider() {
        const sliderContainer = document.querySelector('#priceDropdown .price-slider-visual');

        // Mouse events for thumbs
        minThumb.addEventListener('mousedown', function (e) {
            isDragging = true;
            currentThumb = 'min';
            e.preventDefault();
        });

        maxThumb.addEventListener('mousedown', function (e) {
            isDragging = true;
            currentThumb = 'max';
            e.preventDefault();
        });

        // Click on track to move nearest thumb
        sliderContainer.addEventListener('click', function (e) {
            if (e.target === minThumb || e.target === maxThumb) return;

            const rect = sliderContainer.getBoundingClientRect();
            const clickPercent = ((e.clientX - rect.left) / rect.width) * 100;
            const clickValue = (clickPercent / 100) * maxRange;
            const roundedValue = Math.round(clickValue / stepSize) * stepSize; // Round to nearest thousand

            const minValue = parsePriceInput(minPriceInput.value);
            const maxValue = parsePriceInput(maxPriceInput.value);

            // Move the nearest thumb
            if (Math.abs(roundedValue - minValue) < Math.abs(roundedValue - maxValue)) {
                minPriceInput.value = formatNumberWithDots(roundedValue) + 'đ';
            } else {
                maxPriceInput.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updatePriceInputs();
        });

        // Mouse move event
        document.addEventListener('mousemove', function (e) {
            if (!isDragging) return;

            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize; // Round to nearest thousand

            if (currentThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceInput.value);
                if (value <= maxValue) {
                    minPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentThumb === 'max') {
                const minValue = parsePriceInput(minPriceInput.value);
                if (value >= minValue) {
                    maxPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updatePriceInputs();
        });

        // Mouse up event
        document.addEventListener('mouseup', function () {
            isDragging = false;
            currentThumb = null;
        });

        // Touch events for mobile
        minThumb.addEventListener('touchstart', function (e) {
            isDragging = true;
            currentThumb = 'min';
            e.preventDefault();
        });

        maxThumb.addEventListener('touchstart', function (e) {
            isDragging = true;
            currentThumb = 'max';
            e.preventDefault();
        });

        document.addEventListener('touchmove', function (e) {
            if (!isDragging) return;

            const touch = e.touches[0];
            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize; // Round to nearest thousand

            if (currentThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceInput.value);
                if (value <= maxValue) {
                    minPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentThumb === 'max') {
                const minValue = parsePriceInput(minPriceInput.value);
                if (value >= minValue) {
                    maxPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updatePriceInputs();
            e.preventDefault();
        });

        document.addEventListener('touchend', function () {
            isDragging = false;
            currentThumb = null;
        });
    }

    // Mobile draggable slider functionality
    function initMobileDraggableSlider() {
        if (!minThumbMobile || !maxThumbMobile || !sliderTrackActiveMobile) return;

        const sliderContainer = document.querySelector('#mobilePriceDropdown .price-slider-visual');
        if (!sliderContainer) return;

        let isMobileDragging = false;
        let currentMobileThumb = null;

        // Mouse events for mobile thumbs
        minThumbMobile.addEventListener('mousedown', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'min';
            e.preventDefault();
        });

        maxThumbMobile.addEventListener('mousedown', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'max';
            e.preventDefault();
        });

        // Click on mobile track to move nearest thumb
        sliderContainer.addEventListener('click', function (e) {
            if (e.target === minThumbMobile || e.target === maxThumbMobile) return;

            const rect = sliderContainer.getBoundingClientRect();
            const clickPercent = ((e.clientX - rect.left) / rect.width) * 100;
            const clickValue = (clickPercent / 100) * maxRange;
            const roundedValue = Math.round(clickValue / stepSize) * stepSize;

            const minValue = parsePriceInput(minPriceMobile.value);
            const maxValue = parsePriceInput(maxPriceMobile.value);

            // Move the nearest thumb
            if (Math.abs(roundedValue - minValue) < Math.abs(roundedValue - maxValue)) {
                minPriceMobile.value = formatNumberWithDots(roundedValue) + 'đ';
            } else {
                maxPriceMobile.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updateMobilePriceInputs();
        });

        // Mouse move event for mobile
        document.addEventListener('mousemove', function (e) {
            if (!isMobileDragging) return;

            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize;

            if (currentMobileThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceMobile.value);
                if (value <= maxValue) {
                    minPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentMobileThumb === 'max') {
                const minValue = parsePriceInput(minPriceMobile.value);
                if (value >= minValue) {
                    maxPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updateMobilePriceInputs();
        });

        // Mouse up event for mobile
        document.addEventListener('mouseup', function () {
            isMobileDragging = false;
            currentMobileThumb = null;
        });

        // Touch events for mobile
        minThumbMobile.addEventListener('touchstart', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'min';
            e.preventDefault();
        });

        maxThumbMobile.addEventListener('touchstart', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'max';
            e.preventDefault();
        });

        document.addEventListener('touchmove', function (e) {
            if (!isMobileDragging) return;

            const touch = e.touches[0];
            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize;

            if (currentMobileThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceMobile.value);
                if (value <= maxValue) {
                    minPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentMobileThumb === 'max') {
                const minValue = parsePriceInput(minPriceMobile.value);
                if (value >= minValue) {
                    maxPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updateMobilePriceInputs();
            e.preventDefault();
        });

        document.addEventListener('touchend', function () {
            isMobileDragging = false;
            currentMobileThumb = null;
        });
    }

    // Update slider track visual (popup)
    function updateSliderTrack() {
        const minVal = parseInt(minPriceSlider.value);
        const maxVal = parseInt(maxPriceSlider.value);
        const minRange = parseInt(minPriceSlider.min);
        const maxRange = parseInt(minPriceSlider.max);

        const minPercent = ((minVal - minRange) / (maxRange - minRange)) * 100;
        const maxPercent = ((maxVal - minRange) / (maxRange - minRange)) * 100;

        // Create visual track effect
        const rangeSlider = document.querySelector('.popup-body .range-slider');
        let track = rangeSlider.querySelector('.slider-track');

        if (!track) {
            track = document.createElement('div');
            track.className = 'slider-track';
            track.style.position = 'absolute';
            track.style.height = '6px';
            track.style.background = '#ff6b6b';
            track.style.borderRadius = '3px';
            track.style.top = '17px';
            track.style.zIndex = '1';
            rangeSlider.appendChild(track);
        }

        track.style.left = minPercent + '%';
        track.style.width = (maxPercent - minPercent) + '%';
    }

    // Update slider track visual (desktop dropdown)
    function updateSliderTrackDesktop() {
        const minVal = parseInt(minPriceDesktopSlider.value);
        const maxVal = parseInt(maxPriceDesktopSlider.value);
        const minRange = parseInt(minPriceDesktopSlider.min);
        const maxRange = parseInt(minPriceDesktopSlider.max);

        const minPercent = ((minVal - minRange) / (maxRange - minRange)) * 100;
        const maxPercent = ((maxVal - minRange) / (maxRange - minRange)) * 100;

        // Create visual track effect
        const rangeSlider = document.querySelector('#priceDropdown .range-slider');
        let track = rangeSlider.querySelector('.slider-track');

        if (!track) {
            track = document.createElement('div');
            track.className = 'slider-track';
            track.style.position = 'absolute';
            track.style.height = '6px';
            track.style.background = '#ff6b6b';
            track.style.borderRadius = '3px';
            track.style.top = '17px';
            track.style.zIndex = '1';
            rangeSlider.appendChild(track);
        }

        track.style.left = minPercent + '%';
        track.style.width = (maxPercent - minPercent) + '%';
    }

    // Reset all filters
    function resetFilters() {
        // Reset price sliders (only if they exist)
        if (minPriceSlider && maxPriceSlider) {
            minPriceSlider.value = minPriceSlider.min;
            maxPriceSlider.value = maxPriceSlider.max;
            updatePriceDisplay();
        }

        // Reset all popup tag buttons
        const popupTags = document.querySelectorAll('.popup-tag');
        popupTags.forEach(tag => {
            tag.classList.remove('active');
        });
    }

    // Apply filters
    async function applyFilters() {
        // Update current state from UI
        updateStateFromUI();

        // Send filters to API
        await sendFiltersToAPI(filterState);

        // Close popup with applied = true (don't restore state)
        closePopup(true);
    }

    // Update filter state from current UI
    function updateStateFromUI() {
        console.log('=== UPDATE STATE FROM UI START ===');
        console.log('Current filterState:', JSON.stringify(filterState));

        let priceUpdated = false;

        // Check if we're on mobile or desktop and prioritize the active dropdown
        const isCurrentlyMobile = isMobile();

        // Update price from desktop inputs (only if not mobile or if desktop inputs have non-default values)
        if (minPriceInput && maxPriceInput && minPriceInput.value && maxPriceInput.value) {
            console.log('Desktop input values:', minPriceInput.value, maxPriceInput.value);

            const minPrice = parsePriceInput(minPriceInput.value);
            const maxPrice = parsePriceInput(maxPriceInput.value);

            console.log('Parsed desktop prices:', minPrice, maxPrice);

            // Only update if values are not default OR if we're on desktop
            const isDefaultValues = (minPrice === 0 && maxPrice === 47990000);

            if (minPrice >= 0 && maxPrice >= 0 && (!isDefaultValues || !isCurrentlyMobile)) {
                filterState.price.min = minPrice;
                filterState.price.max = maxPrice;
                priceUpdated = true;
                console.log('Updated price from desktop inputs:', filterState.price);
            } else {
                console.log('Desktop price validation failed or default values on mobile:', minPrice, maxPrice, 'isDefault:', isDefaultValues, 'isMobile:', isCurrentlyMobile);
            }
        } else {
            console.log('Desktop inputs not available or empty:', {
                minPriceInput: !!minPriceInput,
                maxPriceInput: !!maxPriceInput,
                minValue: minPriceInput?.value,
                maxValue: maxPriceInput?.value
            });
        }

        // Update price from mobile inputs (only if not already updated and mobile inputs have non-default values)
        if (!priceUpdated && minPriceMobile && maxPriceMobile && minPriceMobile.value && maxPriceMobile.value) {
            console.log('Mobile input values:', minPriceMobile.value, maxPriceMobile.value);

            const minPrice = parsePriceInput(minPriceMobile.value);
            const maxPrice = parsePriceInput(maxPriceMobile.value);

            console.log('Parsed mobile prices:', minPrice, maxPrice);

            // Only update if values are not default
            const isDefaultValues = (minPrice === 0 && maxPrice === 47990000);

            if (minPrice >= 0 && maxPrice >= 0 && !isDefaultValues) {
                filterState.price.min = minPrice;
                filterState.price.max = maxPrice;
                console.log('Updated price from mobile inputs:', filterState.price);
            } else {
                console.log('Mobile price validation failed or default values:', minPrice, maxPrice, 'isDefault:', isDefaultValues);
            }
        } else {
            console.log('Mobile inputs not checked because price already updated or inputs not available:', {
                priceUpdated,
                minPriceMobile: !!minPriceMobile,
                maxPriceMobile: !!maxPriceMobile,
                minValue: minPriceMobile?.value,
                maxValue: maxPriceMobile?.value
            });
        }

        // Update all multi-select filters dynamically
        Object.keys(FILTER_CONFIG).forEach(filterKey => {
            const config = FILTER_CONFIG[filterKey];

            if (config.type === 'multi-select') {
                filterState[filterKey] = [];

                // From popup tags - use data-filter-type or fallback selectors
                const popupSelectors = [
                    `[data-filter-type="${filterKey}"] .popup-tag.active`,
                    `[data-filter-type="${filterKey}"] .tag-btn.active`,
                    `.${filterKey}-section .popup-tag.active`,
                    `.${filterKey}-section .tag-btn.active`
                ];

                // Legacy support for filter-section index
                if (filterKey === 'features') {
                    popupSelectors.push('.filter-section:nth-child(1) .popup-tag.active');
                } else if (filterKey === 'memory') {
                    popupSelectors.push('.filter-section:nth-child(2) .popup-tag.active');
                }

                popupSelectors.forEach(selector => {
                    document.querySelectorAll(selector).forEach(tag => {
                        const value = tag.textContent.trim();
                        if (value && !filterState[filterKey].includes(value)) {
                            filterState[filterKey].push(value);
                        }
                    });
                });

                // From dropdown checkboxes - use data-filter-type or fallback selectors
                const checkboxSelectors = [
                    `[data-filter-type="${filterKey}"] input[type="checkbox"]:checked`,
                    `#${filterKey}Dropdown input[type="checkbox"]:checked`
                ];

                checkboxSelectors.forEach(selector => {
                    document.querySelectorAll(selector).forEach(checkbox => {
                        const value = checkbox.parentElement.textContent.trim();
                        if (value && !filterState[filterKey].includes(value)) {
                            filterState[filterKey].push(value);
                        }
                    });
                });

                console.log(`Updated ${filterKey} from UI:`, filterState[filterKey]);
            }
        });

        console.log('Updated state from UI:', filterState);
    }

    // Event listeners
    filterBtn.addEventListener('click', function (e) {
        e.stopPropagation(); // Prevent the click from triggering the outside click handler
        openPopup();
    });
    closeBtn.addEventListener('click', closePopup);
    overlay.addEventListener('click', closePopup);
    resetBtn.addEventListener('click', resetFilters);
    applyBtn.addEventListener('click', applyFilters);

    // Prevent popup from closing when clicking inside popup content
    if (filterPopup) {
        filterPopup.addEventListener('click', function (e) {
            e.stopPropagation();
        });

        // Add specific event listener for popup tags
        filterPopup.addEventListener('click', function (e) {
            if (e.target.classList.contains('tag-btn') || e.target.classList.contains('popup-tag')) {
                console.log('Popup tag clicked directly!');
                e.target.classList.toggle('active');
                console.log('Popup tag toggled:', e.target.textContent.trim(), 'Active:', e.target.classList.contains('active'));
                e.stopPropagation();
            }
        });
    }

    // Price slider event listeners (only if elements exist)
    if (minPriceSlider && maxPriceSlider) {
        minPriceSlider.addEventListener('input', updatePriceDisplay);
        maxPriceSlider.addEventListener('input', updatePriceDisplay);
    }

    // Close popup with Escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            if (filterPopup.style.display === 'block') {
                closePopup();
            }
            closeAllDropdowns();
        }
    });

    // Desktop dropdown filter functions
    window.resetPriceFilter = function () {
        if (minPriceInput && maxPriceInput) {
            minPriceInput.value = '0.000đ';
            maxPriceInput.value = '47.990.000đ';
            updatePriceInputs();
        }
        // Don't close dropdown, just reset
    }

    window.closePriceDropdown = function () {
        // Close price dropdown without applying (restore state)
        if (priceDropdown) {
            closeDropdown(priceDropdown, false);
        }
    }

    window.applyPriceFilter = async function () {
        console.log('=== APPLY PRICE FILTER START ===');
        console.log('Before updateStateFromUI - filterState:', JSON.stringify(filterState));
        console.log('Desktop inputs:', minPriceInput?.value, maxPriceInput?.value);
        console.log('Mobile inputs:', minPriceMobile?.value, maxPriceMobile?.value);

        // Update state and send to API
        updateStateFromUI();

        console.log('After updateStateFromUI - filterState:', JSON.stringify(filterState));

        // Update savedState to match current filterState so future restores use the new values
        savedState = {
            price: { ...filterState.price },
            features: [...filterState.features],
            memory: [...filterState.memory]
        };
        console.log('Updated savedState to match current filterState:', JSON.stringify(savedState));

        await sendFiltersToAPI(filterState);

        // Close dropdown with applied = true
        closeAllDropdowns(true);

        console.log('=== APPLY PRICE FILTER END ===');
    }

    window.resetFeaturesFilter = function () {
        const tagBtns = document.querySelectorAll('#featuresDropdown .tag-btn');
        tagBtns.forEach(btn => btn.classList.remove('active'));
        // Don't close dropdown, just reset
    }

    // Generic apply filter function
    window.applyFilter = async function (filterType) {
        console.log(`=== APPLY ${filterType.toUpperCase()} FILTER START ===`);

        // Update state and send to API
        updateStateFromUI();

        // Update savedState to match current filterState
        savedState = initializeFilterState();
        Object.keys(filterState).forEach(key => {
            if (FILTER_CONFIG[key].type === 'range') {
                savedState[key] = { ...filterState[key] };
            } else if (FILTER_CONFIG[key].type === 'multi-select') {
                savedState[key] = [...filterState[key]];
            }
        });

        console.log(`Updated savedState:`, savedState);

        await sendFiltersToAPI(filterState);

        // Close dropdown with applied = true
        closeAllDropdowns(true);

        console.log(`=== APPLY ${filterType.toUpperCase()} FILTER END ===`);
    }

    // Generic reset filter function
    window.resetFilter = function (filterType) {
        console.log(`=== RESET ${filterType.toUpperCase()} FILTER ===`);

        if (!FILTER_CONFIG[filterType] || FILTER_CONFIG[filterType].type !== 'multi-select') {
            console.log(`Invalid filter type for reset: ${filterType}`);
            return;
        }

        // Reset popup tags
        const tagSelectors = [
            `[data-filter-type="${filterType}"] .popup-tag`,
            `[data-filter-type="${filterType}"] .tag-btn`,
            `.${filterType}-section .popup-tag`,
            `.${filterType}-section .tag-btn`
        ];

        // Legacy support
        if (filterType === 'features') {
            tagSelectors.push('.filter-section:nth-child(1) .popup-tag');
        } else if (filterType === 'memory') {
            tagSelectors.push('.filter-section:nth-child(2) .popup-tag');
        }

        tagSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(tag => {
                tag.classList.remove('active');
            });
        });

        // Reset dropdown checkboxes
        const checkboxSelectors = [
            `[data-filter-type="${filterType}"] input[type="checkbox"]`,
            `#${filterType}Dropdown input[type="checkbox"]`
        ];

        checkboxSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(checkbox => {
                checkbox.checked = false;
            });
        });

        console.log(`Reset ${filterType} filter complete`);
    }

    // Specific functions for backward compatibility
    window.applyFeaturesFilter = () => window.applyFilter('features');
    window.resetFeaturesFilter = () => window.resetFilter('features');
    window.applyMemoryFilter = () => window.applyFilter('memory');
    window.resetMemoryFilter = () => window.resetFilter('memory');

    // Functions for new filter types
    window.applyCategoryFilter = () => window.applyFilter('category');
    window.resetCategoryFilter = () => window.resetFilter('category');
    window.applyUsageFilter = () => window.applyFilter('usage');
    window.resetUsageFilter = () => window.resetFilter('usage');
    window.applyBrandFilter = () => window.applyFilter('brand');
    window.resetBrandFilter = () => window.resetFilter('brand');

    // Global filter functions
    window.applyAllFilters = async function () {
        console.log('=== APPLY ALL FILTERS START ===');

        // Update state and send to API
        updateStateFromUI();

        // Update savedState to match current filterState
        savedState = initializeFilterState();
        Object.keys(filterState).forEach(key => {
            if (FILTER_CONFIG[key].type === 'range') {
                savedState[key] = { ...filterState[key] };
            } else if (FILTER_CONFIG[key].type === 'multi-select') {
                savedState[key] = [...filterState[key]];
            }
        });

        console.log('Updated savedState:', savedState);

        await sendFiltersToAPI(filterState);

        // Close popup
        closePopup();

        console.log('=== APPLY ALL FILTERS END ===');
    }

    window.resetAllFilters = function () {
        console.log('=== RESET ALL FILTERS ===');

        // Reset all multi-select filters
        Object.keys(FILTER_CONFIG).forEach(filterKey => {
            if (FILTER_CONFIG[filterKey].type === 'multi-select') {
                window.resetFilter(filterKey);
            }
        });

        // Reset price filter
        if (minPriceInput && maxPriceInput) {
            minPriceInput.value = '0đ';
            maxPriceInput.value = '47.990.000đ';
        }
        if (minPriceMobile && maxPriceMobile) {
            minPriceMobile.value = '0đ';
            maxPriceMobile.value = '47.990.000đ';
        }

        // Reset filter state
        filterState = initializeFilterState();

        console.log('Reset all filters complete');
    }

    // Demo function to display filter results
    function updateFilterResults(data) {
        const resultsDiv = document.getElementById('filterResults');
        if (!resultsDiv) return;

        let html = '<div class="filter-summary">';
        html += `<h3>Tìm thấy ${data.total} sản phẩm</h3>`;

        // Display active filters
        html += '<div class="active-filters">';
        html += '<h4>Bộ lọc đang áp dụng:</h4>';

        Object.keys(filterState).forEach(filterKey => {
            const config = FILTER_CONFIG[filterKey];

            if (config.type === 'range' && (filterState[filterKey].min !== config.defaultValue.min || filterState[filterKey].max !== config.defaultValue.max)) {
                html += `<div class="filter-item">`;
                html += `<strong>${getFilterDisplayName(filterKey)}:</strong> `;
                html += `${formatPrice(filterState[filterKey].min)} - ${formatPrice(filterState[filterKey].max)}`;
                html += `</div>`;
            } else if (config.type === 'multi-select' && filterState[filterKey].length > 0) {
                html += `<div class="filter-item">`;
                html += `<strong>${getFilterDisplayName(filterKey)}:</strong> `;
                html += filterState[filterKey].join(', ');
                html += `</div>`;
            }
        });

        html += '</div>';

        // Display products (demo)
        if (data.products && data.products.length > 0) {
            html += '<div class="products-list">';
            html += '<h4>Sản phẩm:</h4>';
            data.products.forEach(product => {
                html += `<div class="product-item">`;
                html += `<strong>${product.name}</strong> - ${formatPrice(product.price)}`;
                html += `</div>`;
            });
            html += '</div>';
        }

        html += '</div>';
        resultsDiv.innerHTML = html;
    }

    // Helper function to get display name for filter
    function getFilterDisplayName(filterKey) {
        const displayNames = {
            price: 'Mức giá',
            features: 'Tính năng',
            memory: 'Bộ nhớ',
            category: 'Thể loại',
            usage: 'Nhu cầu sử dụng',
            brand: 'Thương hiệu'
        };
        return displayNames[filterKey] || filterKey;
    }

    // Helper function to format price
    function formatPrice(price) {
        if (price >= 1000000) {
            return (price / 1000000).toFixed(1).replace('.0', '') + ' triệu';
        } else if (price >= 1000) {
            return (price / 1000).toFixed(0) + 'k';
        }
        return price.toLocaleString('vi-VN') + 'đ';
    }

    // Mobile price filter functions
    window.resetMobilePriceFilter = function () {
        if (minPriceMobile && maxPriceMobile) {
            minPriceMobile.value = '0.000đ';
            maxPriceMobile.value = '47.990.000đ';
        }
        // Don't close dropdown, just reset
    }

    window.closeMobilePriceDropdown = function () {
        // Close mobile price dropdown without applying (restore state)
        if (mobilePriceDropdown) {
            closeDropdown(mobilePriceDropdown, false);
        }
    }

    window.applyMobilePriceFilter = async function () {
        // Update state and send to API
        updateStateFromUI();

        // Update savedState to match current filterState
        savedState = {
            price: { ...filterState.price },
            features: [...filterState.features],
            memory: [...filterState.memory]
        };

        await sendFiltersToAPI(filterState);

        // Close dropdown with applied = true
        closeAllDropdowns(true);
    }

    // Initialize price displays (only if elements exist)
    if (minPriceSlider && maxPriceSlider) {
        updatePriceDisplay();
    }

    // Initialize price inputs with default values
    if (minPriceInput && maxPriceInput) {
        minPriceInput.value = formatPrice(filterState.price.min);
        maxPriceInput.value = formatPrice(filterState.price.max);
        updatePriceInputs();

        // Initialize draggable slider
        initDraggableSlider();
    }

    // Initialize mobile price inputs
    if (minPriceMobile && maxPriceMobile) {
        minPriceMobile.value = formatPrice(filterState.price.min);
        maxPriceMobile.value = formatPrice(filterState.price.max);

        // Initialize mobile visual slider
        updateMobilePriceInputs();
        initMobileDraggableSlider();

        // Mobile price input event listeners
        minPriceMobile.addEventListener('input', function () {
            let value = this.value.replace(/\D/g, '');
            if (value) {
                const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
                this.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updateMobilePriceInputs();
        });

        maxPriceMobile.addEventListener('input', function () {
            let value = this.value.replace(/\D/g, '');
            if (value) {
                const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
                this.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updateMobilePriceInputs();
        });
    }

    // Desktop dropdown event listeners
    priceBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(priceDropdown);
    });

    featuresBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(featuresDropdown);
    });

    memoryBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(memoryDropdown);
    });

    // Mobile price button event listener
    const mobilePriceBtn = document.getElementById('mobilePriceBtn');
    if (mobilePriceBtn && mobilePriceDropdown) {
        mobilePriceBtn.addEventListener('click', function (e) {
            e.stopPropagation();

            if (isMobile()) {
                // Calculate position for mobile dropdown
                const filterBar = document.querySelector('.filter-bar');
                const rect = filterBar.getBoundingClientRect();

                // Position dropdown right below the filter bar
                mobilePriceDropdown.style.top = (rect.bottom + 8) + 'px';
            }

            toggleDropdown(mobilePriceDropdown);
        });
    }

    // Price input event listeners
    minPriceInput.addEventListener('input', function () {
        // Format input as user types
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        if (value) {
            // Round to nearest thousand
            const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
            this.value = formatNumberWithDots(roundedValue) + 'đ';
        }
        updatePriceInputs();
    });

    maxPriceInput.addEventListener('input', function () {
        // Format input as user types
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        if (value) {
            // Round to nearest thousand
            const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
            this.value = formatNumberWithDots(roundedValue) + 'đ';
        }
        updatePriceInputs();
    });

    // Generic function to get filter type from element
    function getFilterTypeFromElement(element) {
        // Check data-filter-type attribute first
        const filterType = element.getAttribute('data-filter-type') ||
            element.closest('[data-filter-type]')?.getAttribute('data-filter-type');

        if (filterType) return filterType;

        // Fallback: detect from parent containers
        if (element.closest('#featuresDropdown') || element.closest('.features-section')) return 'features';
        if (element.closest('#memoryDropdown') || element.closest('.memory-section')) return 'memory';
        if (element.closest('#categoryDropdown') || element.closest('.category-section')) return 'category';
        if (element.closest('#usageDropdown') || element.closest('.usage-section')) return 'usage';
        if (element.closest('#brandDropdown') || element.closest('.brand-section')) return 'brand';

        // Try to detect from filter-section index (legacy support)
        const filterSection = element.closest('.filter-section');
        if (filterSection) {
            const sections = Array.from(document.querySelectorAll('.filter-section'));
            const index = sections.indexOf(filterSection);
            const filterKeys = Object.keys(FILTER_CONFIG).filter(key => FILTER_CONFIG[key].type === 'multi-select');
            return filterKeys[index] || null;
        }

        return null;
    }

    // Generic sync function for any filter type
    function syncFilterElements(text, isActive, filterType) {
        console.log(`=== SYNC FILTER ELEMENTS ===`);
        console.log(`Syncing ${filterType} "${text}" to ${isActive ? 'active/checked' : 'inactive/unchecked'}`);

        if (!FILTER_CONFIG[filterType] || FILTER_CONFIG[filterType].type !== 'multi-select') {
            console.log(`Invalid filter type: ${filterType}`);
            return;
        }

        // Sync popup tags - use data-filter-type or fallback selectors
        const popupSelectors = [
            `[data-filter-type="${filterType}"] .popup-tag`,
            `[data-filter-type="${filterType}"] .tag-btn`,
            `.${filterType}-section .popup-tag`,
            `.${filterType}-section .tag-btn`
        ];

        popupSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                console.log(`Found ${elements.length} tag elements for selector: ${selector}`);

                elements.forEach(tag => {
                    const tagText = tag.textContent.trim();
                    if (tagText === text) {
                        const wasActive = tag.classList.contains('active');
                        tag.classList.toggle('active', isActive);
                        console.log(`Synced tag "${text}": ${wasActive} → ${isActive}`);
                    }
                });
            }
        });

        // Sync dropdown checkboxes - use data-filter-type or fallback selectors
        const dropdownSelectors = [
            `[data-filter-type="${filterType}"] input[type="checkbox"]`,
            `#${filterType}Dropdown input[type="checkbox"]`
        ];

        dropdownSelectors.forEach(selector => {
            const checkboxes = document.querySelectorAll(selector);
            if (checkboxes.length > 0) {
                console.log(`Found ${checkboxes.length} checkboxes for selector: ${selector}`);

                checkboxes.forEach(checkbox => {
                    const checkboxText = checkbox.parentElement.textContent.trim();
                    if (checkboxText === text) {
                        const wasChecked = checkbox.checked;
                        checkbox.checked = isActive;
                        console.log(`Synced checkbox "${text}": ${wasChecked} → ${isActive}`);
                    }
                });
            }
        });

        console.log(`=== SYNC COMPLETE ===`);
    }

    // Generic tag button event listeners (for popup tags)
    document.addEventListener('click', function (e) {
        console.log('=== CLICK EVENT ===');
        console.log('Clicked element:', e.target);
        console.log('Element classes:', e.target.className);
        console.log('Element text:', e.target.textContent.trim());
        console.log('Has tag-btn class:', e.target.classList.contains('tag-btn'));
        console.log('Has popup-tag class:', e.target.classList.contains('popup-tag'));

        if (e.target.classList.contains('tag-btn') || e.target.classList.contains('popup-tag')) {
            console.log('=== TAG BUTTON CLICKED ===');

            const tagText = e.target.textContent.trim();
            const wasActive = e.target.classList.contains('active');

            console.log('Tag text:', tagText);
            console.log('Was active before toggle:', wasActive);

            e.target.classList.toggle('active');
            const isActive = e.target.classList.contains('active');

            console.log('Is active after toggle:', isActive);
            console.log('Element classes after toggle:', e.target.className);

            // Determine filter type dynamically
            const filterType = getFilterTypeFromElement(e.target);
            console.log('Detected filter type:', filterType);

            if (filterType) {
                syncFilterElements(tagText, isActive, filterType);
            } else {
                console.log('Could not determine filter type!');
            }

            e.stopPropagation(); // Prevent dropdown/popup from closing
        }
    });

    // Generic checkbox event listeners (for dropdown checkboxes)
    document.addEventListener('change', function (e) {
        if (e.target.type === 'checkbox') {
            console.log('=== CHECKBOX CHANGED ===');

            const checkboxText = e.target.parentElement.textContent.trim();
            const isChecked = e.target.checked;

            // Determine filter type dynamically
            const filterType = getFilterTypeFromElement(e.target);

            console.log('Checkbox text:', checkboxText);
            console.log('Is checked:', isChecked);
            console.log('Detected filter type:', filterType);

            if (filterType) {
                syncFilterElements(checkboxText, isChecked, filterType);
            } else {
                console.log('Could not determine filter type from checkbox!');
            }
        }
    });

    // Prevent dropdowns from closing when clicking inside them
    document.querySelectorAll('.filter-dropdown').forEach(dropdown => {
        dropdown.addEventListener('click', function (e) {
            e.stopPropagation();
        });
    });

    // Close dropdowns when clicking outside, but not when clicking inside
    document.addEventListener('click', function (e) {
        // Don't close if clicking inside any dropdown or popup
        if (e.target.closest('.filter-dropdown') ||
            e.target.closest('.filter-popup') ||
            e.target.closest('.filter-dropdown-container') ||
            e.target.closest('.mobile-price-container') ||
            e.target.closest('.filter-btn') ||
            e.target.closest('.mobile-price-btn')) {
            return;
        }

        console.log('Clicking outside - closing dropdowns');

        // Close all dropdowns if clicking outside
        closeAllDropdowns(false); // false = restore state

        // Close popup if clicking outside
        if (filterPopup.style.display === 'block') {
            closePopup(false); // false = restore state
        }
    });

    // Add hover effects for desktop filter buttons
    const desktopFilterBtns = document.querySelectorAll('.desktop-filters .filter-btn');
    desktopFilterBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(180deg)';
            }
        });

        btn.addEventListener('mouseleave', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0deg)';
            }
        });
    });
});
