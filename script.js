document.addEventListener('DOMContentLoaded', function () {
    // Elements
    const filterBtn = document.getElementById('filterBtn');
    const filterPopup = document.getElementById('filterPopup');
    const overlay = document.getElementById('overlay');
    const closeBtn = document.getElementById('closeBtn');
    const resetBtn = document.getElementById('resetBtn');
    const applyBtn = document.getElementById('applyBtn');

    // Desktop dropdown elements
    const priceBtn = document.getElementById('priceBtn');
    const featuresBtn = document.getElementById('featuresBtn');
    const memoryBtn = document.getElementById('memoryBtn');
    const priceDropdown = document.getElementById('priceDropdown');
    const featuresDropdown = document.getElementById('featuresDropdown');
    const memoryDropdown = document.getElementById('memoryDropdown');

    // Price range elements (popup)
    const minPriceSlider = document.getElementById('minPrice');
    const maxPriceSlider = document.getElementById('maxPrice');
    const minPriceDisplay = document.getElementById('minPriceDisplay');
    const maxPriceDisplay = document.getElementById('maxPriceDisplay');

    // Price input elements (desktop dropdown)
    const minPriceInput = document.getElementById('minPriceInput');
    const maxPriceInput = document.getElementById('maxPriceInput');
    const sliderTrackActive = document.getElementById('sliderTrackActive');
    const minThumb = document.getElementById('minThumb');
    const maxThumb = document.getElementById('maxThumb');

    // Mobile price elements
    const minPriceMobile = document.getElementById('minPriceMobile');
    const maxPriceMobile = document.getElementById('maxPriceMobile');
    const sliderTrackActiveMobile = document.getElementById('sliderTrackActiveMobile');
    const minThumbMobile = document.getElementById('minThumbMobile');
    const maxThumbMobile = document.getElementById('maxThumbMobile');
    const mobilePriceDropdown = document.getElementById('mobilePriceDropdown');

    // State management for filters
    let filterState = {
        price: { min: 0, max: 47990000 },
        features: [],
        memory: []
    };

    let savedState = {
        price: { min: 0, max: 47990000 },
        features: [],
        memory: []
    }; // Backup state when opening dropdown/popup

    // Check if mobile
    function isMobile() {
        return window.innerWidth <= 768;
    }

    // Save current filter state
    function saveCurrentState() {
        // Ensure filterState is valid before saving
        if (!filterState || typeof filterState !== 'object') {
            console.warn('Invalid filterState, initializing with defaults');
            filterState = {
                price: { min: 0, max: 47990000 },
                features: [],
                memory: []
            };
        }

        savedState = {
            price: filterState.price ? { ...filterState.price } : { min: 0, max: 47990000 },
            features: Array.isArray(filterState.features) ? [...filterState.features] : [],
            memory: Array.isArray(filterState.memory) ? [...filterState.memory] : []
        };
        console.log('Saved state:', savedState);
    }

    // Restore saved state
    function restoreSavedState() {
        // Validate savedState before restoring
        if (!savedState || typeof savedState !== 'object') {
            console.warn('Invalid savedState, using default state');
            return;
        }

        filterState = {
            price: savedState.price ? { ...savedState.price } : { min: 0, max: 47990000 },
            features: Array.isArray(savedState.features) ? [...savedState.features] : [],
            memory: Array.isArray(savedState.memory) ? [...savedState.memory] : []
        };

        // Update UI to reflect restored state
        updateUIFromState();
        console.log('Restored state:', filterState);
    }

    // Update UI elements based on current state
    function updateUIFromState() {
        // Validate filterState before updating UI
        if (!filterState || typeof filterState !== 'object') {
            console.warn('Invalid filterState in updateUIFromState');
            return;
        }

        // Update price inputs
        if (minPriceInput && maxPriceInput && filterState.price) {
            const minValue = formatPrice(filterState.price.min || 0);
            const maxValue = formatPrice(filterState.price.max || 47990000);

            minPriceInput.value = minValue;
            maxPriceInput.value = maxValue;

            console.log('Updated desktop price inputs:', minValue, maxValue);

            if (typeof updatePriceInputs === 'function') {
                updatePriceInputs();
            }
        }

        if (minPriceMobile && maxPriceMobile && filterState.price) {
            const minValue = formatPrice(filterState.price.min || 0);
            const maxValue = formatPrice(filterState.price.max || 47990000);

            minPriceMobile.value = minValue;
            maxPriceMobile.value = maxValue;

            console.log('Updated mobile price inputs:', minValue, maxValue);

            if (typeof updateMobilePriceInputs === 'function') {
                updateMobilePriceInputs();
            }
        }

        // Update feature tags
        if (Array.isArray(filterState.features)) {
            document.querySelectorAll('#featuresDropdown .tag-btn, .filter-popup .features-section .tag-btn, .filter-section:nth-child(1) .popup-tag').forEach(btn => {
                const isActive = filterState.features.includes(btn.textContent.trim());
                btn.classList.toggle('active', isActive);
            });
        }

        // Update memory tags
        if (Array.isArray(filterState.memory)) {
            document.querySelectorAll('#memoryDropdown .tag-btn, .filter-popup .memory-section .tag-btn, .filter-section:nth-child(2) .popup-tag').forEach(btn => {
                const isActive = filterState.memory.includes(btn.textContent.trim());
                btn.classList.toggle('active', isActive);
            });
        }
    }

    // Send filters to API
    async function sendFiltersToAPI(filters) {
        try {
            console.log('Sending filters to API:', filters);

            // Replace with your actual API endpoint
            const response = await fetch('/api/products/filter', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(filters)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('API response:', data);

            // Handle successful response
            handleAPIResponse(data);
            return data;

        } catch (error) {
            console.error('API Error:', error);

            // For demo purposes, simulate API response
            const mockData = {
                products: [
                    { id: 1, name: 'iPhone 15 Pro', price: 25000000 },
                    { id: 2, name: 'Samsung Galaxy S24', price: 20000000 }
                ],
                total: 2,
                filters: filters
            };

            setTimeout(() => {
                handleAPIResponse(mockData);
            }, 500);

            return mockData;
        }
    }

    // Handle API response
    function handleAPIResponse(data) {
        console.log('Processing API response:', data);

        // Update UI with filtered products
        // You can update product list, pagination, etc.
        alert(`Tìm thấy ${data.total} sản phẩm phù hợp với bộ lọc!`);
    }

    // Open popup
    function openPopup() {
        saveCurrentState(); // Save state before opening

        filterPopup.style.display = 'block';
        overlay.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // Update popup UI to reflect current state
        updateUIFromState();

        if (isMobile()) {
            // Mobile animation
            setTimeout(() => {
                filterPopup.classList.add('show');
                overlay.classList.add('show');
            }, 10);
        } else {
            // Desktop - no animation needed
            overlay.classList.add('show');
        }
    }

    // Close popup (restore state if not applied)
    function closePopup(applied = false) {
        if (!applied) {
            restoreSavedState(); // Restore previous state if not applied
        }

        if (isMobile()) {
            // Mobile animation
            filterPopup.classList.remove('show');
            overlay.classList.remove('show');

            setTimeout(() => {
                filterPopup.style.display = 'none';
                overlay.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        } else {
            // Desktop - immediate close
            filterPopup.style.display = 'none';
            overlay.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    }

    // Dropdown functions
    function toggleDropdown(dropdown) {
        // Close all other dropdowns
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => {
            if (dd !== dropdown) {
                closeDropdown(dd);
            }
        });

        // Toggle current dropdown
        if (dropdown.classList.contains('active')) {
            closeDropdown(dropdown);
        } else {
            openDropdown(dropdown);
        }
    }

    function openDropdown(dropdown) {
        saveCurrentState(); // Save state before opening dropdown

        dropdown.classList.add('active');
        dropdown.style.display = 'block';

        // Update UI to reflect current state when opening
        updateUIFromState();

        if (isMobile()) {
            setTimeout(() => {
                dropdown.classList.add('show');
            }, 10);
        }
    }

    function closeDropdown(dropdown, applied = false) {
        if (!applied) {
            restoreSavedState(); // Restore previous state if not applied
        }

        if (isMobile()) {
            dropdown.classList.remove('show');
            setTimeout(() => {
                dropdown.classList.remove('active');
                dropdown.style.display = 'none';
            }, 300);
        } else {
            dropdown.classList.remove('active');
            dropdown.style.display = 'none';
        }
    }

    function closeAllDropdowns(applied = false) {
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => closeDropdown(dd, applied));
    }

    // Format price display
    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN').format(price) + 'đ';
    }

    // Update price display (popup)
    function updatePriceDisplay() {
        const minPrice = parseInt(minPriceSlider.value);
        const maxPrice = parseInt(maxPriceSlider.value);

        // Ensure min is not greater than max
        if (minPrice > maxPrice) {
            minPriceSlider.value = maxPrice;
        }
        if (maxPrice < minPrice) {
            maxPriceSlider.value = minPrice;
        }

        minPriceDisplay.textContent = formatPrice(minPriceSlider.value);
        maxPriceDisplay.textContent = formatPrice(maxPriceSlider.value);

        // Update slider track color
        updateSliderTrack();
    }

    // Format number with dots
    function formatNumberWithDots(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }

    // Parse price from input (remove dots and 'đ')
    function parsePriceInput(value) {
        return parseInt(value.replace(/\./g, '').replace('đ', '')) || 0;
    }

    // Update price inputs and visual slider
    function updatePriceInputs() {
        const minValue = parsePriceInput(minPriceInput.value);
        const maxValue = parsePriceInput(maxPriceInput.value);

        // Update visual slider
        updateVisualSlider(minValue, maxValue);
    }

    // Update mobile price inputs and visual slider
    function updateMobilePriceInputs() {
        if (!minPriceMobile || !maxPriceMobile) return;

        const minValue = parsePriceInput(minPriceMobile.value);
        const maxValue = parsePriceInput(maxPriceMobile.value);

        // Update mobile visual slider
        updateMobileVisualSlider(minValue, maxValue);
    }

    // Update visual slider position
    function updateVisualSlider(minValue, maxValue) {
        if (!sliderTrackActive || !minThumb || !maxThumb) return;

        const maxRange = 50000000; // 50 million
        const minPercent = Math.max(0, Math.min(100, (minValue / maxRange) * 100));
        const maxPercent = Math.max(0, Math.min(100, (maxValue / maxRange) * 100));

        // Update active track
        sliderTrackActive.style.left = minPercent + '%';
        sliderTrackActive.style.width = (maxPercent - minPercent) + '%';

        // Update thumbs
        minThumb.style.left = minPercent + '%';
        maxThumb.style.left = maxPercent + '%';
    }

    // Update mobile visual slider position
    function updateMobileVisualSlider(minValue, maxValue) {
        if (!sliderTrackActiveMobile || !minThumbMobile || !maxThumbMobile) return;

        const maxRange = 50000000; // 50 million
        const minPercent = Math.max(0, Math.min(100, (minValue / maxRange) * 100));
        const maxPercent = Math.max(0, Math.min(100, (maxValue / maxRange) * 100));

        // Update active track
        sliderTrackActiveMobile.style.left = minPercent + '%';
        sliderTrackActiveMobile.style.width = (maxPercent - minPercent) + '%';

        // Update thumbs
        minThumbMobile.style.left = minPercent + '%';
        maxThumbMobile.style.left = maxPercent + '%';
    }

    // Draggable slider functionality
    let isDragging = false;
    let currentThumb = null;
    const maxRange = 50000000;
    const stepSize = 1000; // Step by 1,000 VND

    function initDraggableSlider() {
        const sliderContainer = document.querySelector('#priceDropdown .price-slider-visual');

        // Mouse events for thumbs
        minThumb.addEventListener('mousedown', function (e) {
            isDragging = true;
            currentThumb = 'min';
            e.preventDefault();
        });

        maxThumb.addEventListener('mousedown', function (e) {
            isDragging = true;
            currentThumb = 'max';
            e.preventDefault();
        });

        // Click on track to move nearest thumb
        sliderContainer.addEventListener('click', function (e) {
            if (e.target === minThumb || e.target === maxThumb) return;

            const rect = sliderContainer.getBoundingClientRect();
            const clickPercent = ((e.clientX - rect.left) / rect.width) * 100;
            const clickValue = (clickPercent / 100) * maxRange;
            const roundedValue = Math.round(clickValue / stepSize) * stepSize; // Round to nearest thousand

            const minValue = parsePriceInput(minPriceInput.value);
            const maxValue = parsePriceInput(maxPriceInput.value);

            // Move the nearest thumb
            if (Math.abs(roundedValue - minValue) < Math.abs(roundedValue - maxValue)) {
                minPriceInput.value = formatNumberWithDots(roundedValue) + 'đ';
            } else {
                maxPriceInput.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updatePriceInputs();
        });

        // Mouse move event
        document.addEventListener('mousemove', function (e) {
            if (!isDragging) return;

            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize; // Round to nearest thousand

            if (currentThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceInput.value);
                if (value <= maxValue) {
                    minPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentThumb === 'max') {
                const minValue = parsePriceInput(minPriceInput.value);
                if (value >= minValue) {
                    maxPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updatePriceInputs();
        });

        // Mouse up event
        document.addEventListener('mouseup', function () {
            isDragging = false;
            currentThumb = null;
        });

        // Touch events for mobile
        minThumb.addEventListener('touchstart', function (e) {
            isDragging = true;
            currentThumb = 'min';
            e.preventDefault();
        });

        maxThumb.addEventListener('touchstart', function (e) {
            isDragging = true;
            currentThumb = 'max';
            e.preventDefault();
        });

        document.addEventListener('touchmove', function (e) {
            if (!isDragging) return;

            const touch = e.touches[0];
            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize; // Round to nearest thousand

            if (currentThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceInput.value);
                if (value <= maxValue) {
                    minPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentThumb === 'max') {
                const minValue = parsePriceInput(minPriceInput.value);
                if (value >= minValue) {
                    maxPriceInput.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updatePriceInputs();
            e.preventDefault();
        });

        document.addEventListener('touchend', function () {
            isDragging = false;
            currentThumb = null;
        });
    }

    // Mobile draggable slider functionality
    function initMobileDraggableSlider() {
        if (!minThumbMobile || !maxThumbMobile || !sliderTrackActiveMobile) return;

        const sliderContainer = document.querySelector('#mobilePriceDropdown .price-slider-visual');
        if (!sliderContainer) return;

        let isMobileDragging = false;
        let currentMobileThumb = null;

        // Mouse events for mobile thumbs
        minThumbMobile.addEventListener('mousedown', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'min';
            e.preventDefault();
        });

        maxThumbMobile.addEventListener('mousedown', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'max';
            e.preventDefault();
        });

        // Click on mobile track to move nearest thumb
        sliderContainer.addEventListener('click', function (e) {
            if (e.target === minThumbMobile || e.target === maxThumbMobile) return;

            const rect = sliderContainer.getBoundingClientRect();
            const clickPercent = ((e.clientX - rect.left) / rect.width) * 100;
            const clickValue = (clickPercent / 100) * maxRange;
            const roundedValue = Math.round(clickValue / stepSize) * stepSize;

            const minValue = parsePriceInput(minPriceMobile.value);
            const maxValue = parsePriceInput(maxPriceMobile.value);

            // Move the nearest thumb
            if (Math.abs(roundedValue - minValue) < Math.abs(roundedValue - maxValue)) {
                minPriceMobile.value = formatNumberWithDots(roundedValue) + 'đ';
            } else {
                maxPriceMobile.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updateMobilePriceInputs();
        });

        // Mouse move event for mobile
        document.addEventListener('mousemove', function (e) {
            if (!isMobileDragging) return;

            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize;

            if (currentMobileThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceMobile.value);
                if (value <= maxValue) {
                    minPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentMobileThumb === 'max') {
                const minValue = parsePriceInput(minPriceMobile.value);
                if (value >= minValue) {
                    maxPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updateMobilePriceInputs();
        });

        // Mouse up event for mobile
        document.addEventListener('mouseup', function () {
            isMobileDragging = false;
            currentMobileThumb = null;
        });

        // Touch events for mobile
        minThumbMobile.addEventListener('touchstart', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'min';
            e.preventDefault();
        });

        maxThumbMobile.addEventListener('touchstart', function (e) {
            isMobileDragging = true;
            currentMobileThumb = 'max';
            e.preventDefault();
        });

        document.addEventListener('touchmove', function (e) {
            if (!isMobileDragging) return;

            const touch = e.touches[0];
            const rect = sliderContainer.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100));
            const value = Math.round((percent / 100) * maxRange / stepSize) * stepSize;

            if (currentMobileThumb === 'min') {
                const maxValue = parsePriceInput(maxPriceMobile.value);
                if (value <= maxValue) {
                    minPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            } else if (currentMobileThumb === 'max') {
                const minValue = parsePriceInput(minPriceMobile.value);
                if (value >= minValue) {
                    maxPriceMobile.value = formatNumberWithDots(value) + 'đ';
                }
            }
            updateMobilePriceInputs();
            e.preventDefault();
        });

        document.addEventListener('touchend', function () {
            isMobileDragging = false;
            currentMobileThumb = null;
        });
    }

    // Update slider track visual (popup)
    function updateSliderTrack() {
        const minVal = parseInt(minPriceSlider.value);
        const maxVal = parseInt(maxPriceSlider.value);
        const minRange = parseInt(minPriceSlider.min);
        const maxRange = parseInt(minPriceSlider.max);

        const minPercent = ((minVal - minRange) / (maxRange - minRange)) * 100;
        const maxPercent = ((maxVal - minRange) / (maxRange - minRange)) * 100;

        // Create visual track effect
        const rangeSlider = document.querySelector('.popup-body .range-slider');
        let track = rangeSlider.querySelector('.slider-track');

        if (!track) {
            track = document.createElement('div');
            track.className = 'slider-track';
            track.style.position = 'absolute';
            track.style.height = '6px';
            track.style.background = '#ff6b6b';
            track.style.borderRadius = '3px';
            track.style.top = '17px';
            track.style.zIndex = '1';
            rangeSlider.appendChild(track);
        }

        track.style.left = minPercent + '%';
        track.style.width = (maxPercent - minPercent) + '%';
    }

    // Update slider track visual (desktop dropdown)
    function updateSliderTrackDesktop() {
        const minVal = parseInt(minPriceDesktopSlider.value);
        const maxVal = parseInt(maxPriceDesktopSlider.value);
        const minRange = parseInt(minPriceDesktopSlider.min);
        const maxRange = parseInt(minPriceDesktopSlider.max);

        const minPercent = ((minVal - minRange) / (maxRange - minRange)) * 100;
        const maxPercent = ((maxVal - minRange) / (maxRange - minRange)) * 100;

        // Create visual track effect
        const rangeSlider = document.querySelector('#priceDropdown .range-slider');
        let track = rangeSlider.querySelector('.slider-track');

        if (!track) {
            track = document.createElement('div');
            track.className = 'slider-track';
            track.style.position = 'absolute';
            track.style.height = '6px';
            track.style.background = '#ff6b6b';
            track.style.borderRadius = '3px';
            track.style.top = '17px';
            track.style.zIndex = '1';
            rangeSlider.appendChild(track);
        }

        track.style.left = minPercent + '%';
        track.style.width = (maxPercent - minPercent) + '%';
    }

    // Reset all filters
    function resetFilters() {
        // Reset price sliders (only if they exist)
        if (minPriceSlider && maxPriceSlider) {
            minPriceSlider.value = minPriceSlider.min;
            maxPriceSlider.value = maxPriceSlider.max;
            updatePriceDisplay();
        }

        // Reset all popup tag buttons
        const popupTags = document.querySelectorAll('.popup-tag');
        popupTags.forEach(tag => {
            tag.classList.remove('active');
        });
    }

    // Apply filters
    async function applyFilters() {
        // Update current state from UI
        updateStateFromUI();

        // Send filters to API
        await sendFiltersToAPI(filterState);

        // Close popup with applied = true (don't restore state)
        closePopup(true);
    }

    // Update filter state from current UI
    function updateStateFromUI() {
        // Update price from inputs (prioritize the one that's currently visible/active)
        if (minPriceInput && maxPriceInput && minPriceInput.value && maxPriceInput.value) {
            const minPrice = parsePriceInput(minPriceInput.value);
            const maxPrice = parsePriceInput(maxPriceInput.value);
            if (minPrice >= 0 && maxPrice >= 0) {
                filterState.price.min = minPrice;
                filterState.price.max = maxPrice;
                console.log('Updated price from desktop inputs:', filterState.price);
            }
        }

        if (minPriceMobile && maxPriceMobile && minPriceMobile.value && maxPriceMobile.value) {
            const minPrice = parsePriceInput(minPriceMobile.value);
            const maxPrice = parsePriceInput(maxPriceMobile.value);
            if (minPrice >= 0 && maxPrice >= 0) {
                filterState.price.min = minPrice;
                filterState.price.max = maxPrice;
                console.log('Updated price from mobile inputs:', filterState.price);
            }
        }

        // Update features from active tags (both popup and dropdown)
        filterState.features = [];
        document.querySelectorAll('.filter-section:nth-child(1) .popup-tag.active, #featuresDropdown .tag-btn.active, .filter-popup .features-section .tag-btn.active').forEach(tag => {
            const feature = tag.textContent.trim();
            if (!filterState.features.includes(feature)) {
                filterState.features.push(feature);
            }
        });

        // Update memory from active tags (both popup and dropdown)
        filterState.memory = [];
        document.querySelectorAll('.filter-section:nth-child(2) .popup-tag.active, #memoryDropdown .tag-btn.active, .filter-popup .memory-section .tag-btn.active').forEach(tag => {
            const memory = tag.textContent.trim();
            if (!filterState.memory.includes(memory)) {
                filterState.memory.push(memory);
            }
        });

        console.log('Updated state from UI:', filterState);
    }

    // Event listeners
    filterBtn.addEventListener('click', function (e) {
        e.stopPropagation(); // Prevent the click from triggering the outside click handler
        openPopup();
    });
    closeBtn.addEventListener('click', closePopup);
    overlay.addEventListener('click', closePopup);
    resetBtn.addEventListener('click', resetFilters);
    applyBtn.addEventListener('click', applyFilters);

    // Prevent popup from closing when clicking inside popup content
    if (filterPopup) {
        filterPopup.addEventListener('click', function (e) {
            e.stopPropagation();
        });

        // Add specific event listener for popup tags
        filterPopup.addEventListener('click', function (e) {
            if (e.target.classList.contains('tag-btn') || e.target.classList.contains('popup-tag')) {
                console.log('Popup tag clicked directly!');
                e.target.classList.toggle('active');
                console.log('Popup tag toggled:', e.target.textContent.trim(), 'Active:', e.target.classList.contains('active'));
                e.stopPropagation();
            }
        });
    }

    // Price slider event listeners (only if elements exist)
    if (minPriceSlider && maxPriceSlider) {
        minPriceSlider.addEventListener('input', updatePriceDisplay);
        maxPriceSlider.addEventListener('input', updatePriceDisplay);
    }

    // Close popup with Escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            if (filterPopup.style.display === 'block') {
                closePopup();
            }
            closeAllDropdowns();
        }
    });

    // Desktop dropdown filter functions
    window.resetPriceFilter = function () {
        if (minPriceInput && maxPriceInput) {
            minPriceInput.value = '0.000đ';
            maxPriceInput.value = '47.990.000đ';
            updatePriceInputs();
        }
        // Don't close dropdown, just reset
    }

    window.closePriceDropdown = function () {
        // Close price dropdown without applying (restore state)
        if (priceDropdown) {
            closeDropdown(priceDropdown, false);
        }
    }

    window.applyPriceFilter = async function () {
        // Update state and send to API
        updateStateFromUI();
        await sendFiltersToAPI(filterState);

        // Close dropdown with applied = true
        closeAllDropdowns(true);
    }

    window.resetFeaturesFilter = function () {
        const tagBtns = document.querySelectorAll('#featuresDropdown .tag-btn');
        tagBtns.forEach(btn => btn.classList.remove('active'));
        // Don't close dropdown, just reset
    }

    window.applyFeaturesFilter = async function () {
        // Update state and send to API
        updateStateFromUI();
        await sendFiltersToAPI(filterState);

        // Close dropdown with applied = true
        closeAllDropdowns(true);
    }

    window.resetMemoryFilter = function () {
        const tagBtns = document.querySelectorAll('#memoryDropdown .tag-btn');
        tagBtns.forEach(btn => btn.classList.remove('active'));
        // Don't close dropdown, just reset
    }

    window.applyMemoryFilter = async function () {
        // Update state and send to API
        updateStateFromUI();
        await sendFiltersToAPI(filterState);

        // Close dropdown with applied = true
        closeAllDropdowns(true);
    }

    // Mobile price filter functions
    window.resetMobilePriceFilter = function () {
        if (minPriceMobile && maxPriceMobile) {
            minPriceMobile.value = '0.000đ';
            maxPriceMobile.value = '47.990.000đ';
        }
        // Don't close dropdown, just reset
    }

    window.closeMobilePriceDropdown = function () {
        // Close mobile price dropdown without applying (restore state)
        if (mobilePriceDropdown) {
            closeDropdown(mobilePriceDropdown, false);
        }
    }

    window.applyMobilePriceFilter = async function () {
        // Update state and send to API
        updateStateFromUI();
        await sendFiltersToAPI(filterState);

        // Close dropdown with applied = true
        closeAllDropdowns(true);
    }

    // Initialize price displays (only if elements exist)
    if (minPriceSlider && maxPriceSlider) {
        updatePriceDisplay();
    }

    // Initialize price inputs with default values
    if (minPriceInput && maxPriceInput) {
        minPriceInput.value = '0.000đ';
        maxPriceInput.value = '47.990.000đ';
        updatePriceInputs();

        // Initialize draggable slider
        initDraggableSlider();
    }

    // Initialize mobile price inputs
    if (minPriceMobile && maxPriceMobile) {
        minPriceMobile.value = '0.000đ';
        maxPriceMobile.value = '47.990.000đ';

        // Initialize mobile visual slider
        updateMobilePriceInputs();
        initMobileDraggableSlider();

        // Mobile price input event listeners
        minPriceMobile.addEventListener('input', function () {
            let value = this.value.replace(/\D/g, '');
            if (value) {
                const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
                this.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updateMobilePriceInputs();
        });

        maxPriceMobile.addEventListener('input', function () {
            let value = this.value.replace(/\D/g, '');
            if (value) {
                const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
                this.value = formatNumberWithDots(roundedValue) + 'đ';
            }
            updateMobilePriceInputs();
        });
    }

    // Desktop dropdown event listeners
    priceBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(priceDropdown);
    });

    featuresBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(featuresDropdown);
    });

    memoryBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        toggleDropdown(memoryDropdown);
    });

    // Mobile price button event listener
    const mobilePriceBtn = document.getElementById('mobilePriceBtn');
    if (mobilePriceBtn && mobilePriceDropdown) {
        mobilePriceBtn.addEventListener('click', function (e) {
            e.stopPropagation();

            if (isMobile()) {
                // Calculate position for mobile dropdown
                const filterBar = document.querySelector('.filter-bar');
                const rect = filterBar.getBoundingClientRect();

                // Position dropdown right below the filter bar
                mobilePriceDropdown.style.top = (rect.bottom + 8) + 'px';
            }

            toggleDropdown(mobilePriceDropdown);
        });
    }

    // Price input event listeners
    minPriceInput.addEventListener('input', function () {
        // Format input as user types
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        if (value) {
            // Round to nearest thousand
            const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
            this.value = formatNumberWithDots(roundedValue) + 'đ';
        }
        updatePriceInputs();
    });

    maxPriceInput.addEventListener('input', function () {
        // Format input as user types
        let value = this.value.replace(/\D/g, ''); // Remove non-digits
        if (value) {
            // Round to nearest thousand
            const roundedValue = Math.round(parseInt(value) / stepSize) * stepSize;
            this.value = formatNumberWithDots(roundedValue) + 'đ';
        }
        updatePriceInputs();
    });

    // Tag button event listeners (for both popup and dropdown)
    document.addEventListener('click', function (e) {
        console.log('Clicked element:', e.target, 'Classes:', e.target.className);

        if (e.target.classList.contains('tag-btn') || e.target.classList.contains('popup-tag')) {
            console.log('Tag button clicked!');
            e.target.classList.toggle('active');
            console.log('Toggled tag:', e.target.textContent.trim(), 'Active:', e.target.classList.contains('active'));
            console.log('Element classes after toggle:', e.target.className);
            e.stopPropagation(); // Prevent dropdown/popup from closing
        }
    });

    // Prevent dropdowns from closing when clicking inside them
    document.querySelectorAll('.filter-dropdown').forEach(dropdown => {
        dropdown.addEventListener('click', function (e) {
            e.stopPropagation();
        });
    });

    // Close dropdowns when clicking outside, but not when clicking inside
    document.addEventListener('click', function (e) {
        // Don't close if clicking inside any dropdown or popup
        if (e.target.closest('.filter-dropdown') ||
            e.target.closest('.filter-popup') ||
            e.target.closest('.filter-dropdown-container') ||
            e.target.closest('.mobile-price-container') ||
            e.target.closest('.filter-btn') ||
            e.target.closest('.mobile-price-btn')) {
            return;
        }

        // Close all dropdowns if clicking outside
        closeAllDropdowns();

        // Close popup if clicking outside
        if (filterPopup.style.display === 'block') {
            closePopup();
        }
    });

    // Add hover effects for desktop filter buttons
    const desktopFilterBtns = document.querySelectorAll('.desktop-filters .filter-btn');
    desktopFilterBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(180deg)';
            }
        });

        btn.addEventListener('mouseleave', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0deg)';
            }
        });
    });
});
