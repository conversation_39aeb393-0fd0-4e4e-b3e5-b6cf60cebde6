document.addEventListener('DOMContentLoaded', function () {
    // Elements
    const filterBtn = document.getElementById('filterBtn');
    const filterPopup = document.getElementById('filterPopup');
    const overlay = document.getElementById('overlay');
    const closeBtn = document.getElementById('closeBtn');
    const resetBtn = document.getElementById('resetBtn');
    const applyBtn = document.getElementById('applyBtn');

    // Desktop dropdown elements
    const priceBtn = document.getElementById('priceBtn');
    const featuresBtn = document.getElementById('featuresBtn');
    const memoryBtn = document.getElementById('memoryBtn');
    const priceDropdown = document.getElementById('priceDropdown');
    const featuresDropdown = document.getElementById('featuresDropdown');
    const memoryDropdown = document.getElementById('memoryDropdown');

    // Price range elements (popup)
    const minPriceSlider = document.getElementById('minPrice');
    const maxPriceSlider = document.getElementById('maxPrice');
    const minPriceDisplay = document.getElementById('minPriceDisplay');
    const maxPriceDisplay = document.getElementById('maxPriceDisplay');

    // Price range elements (desktop dropdown)
    const minPriceDesktopSlider = document.getElementById('minPriceDesktop');
    const maxPriceDesktopSlider = document.getElementById('maxPriceDesktop');
    const minPriceDesktopDisplay = document.getElementById('minPriceDesktopDisplay');
    const maxPriceDesktopDisplay = document.getElementById('maxPriceDesktopDisplay');

    // Open popup
    function openPopup() {
        filterPopup.style.display = 'block';
        overlay.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    // Close popup
    function closePopup() {
        filterPopup.style.display = 'none';
        overlay.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // Dropdown functions
    function toggleDropdown(dropdown) {
        // Close all other dropdowns
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => {
            if (dd !== dropdown) {
                dd.classList.remove('active');
            }
        });

        // Toggle current dropdown
        dropdown.classList.toggle('active');
    }

    function closeAllDropdowns() {
        const allDropdowns = document.querySelectorAll('.filter-dropdown');
        allDropdowns.forEach(dd => dd.classList.remove('active'));
    }

    // Format price display
    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN').format(price) + 'đ';
    }

    // Update price display
    function updatePriceDisplay() {
        const minPrice = parseInt(minPriceSlider.value);
        const maxPrice = parseInt(maxPriceSlider.value);

        // Ensure min is not greater than max
        if (minPrice > maxPrice) {
            minPriceSlider.value = maxPrice;
        }
        if (maxPrice < minPrice) {
            maxPriceSlider.value = minPrice;
        }

        minPriceDisplay.textContent = formatPrice(minPriceSlider.value);
        maxPriceDisplay.textContent = formatPrice(maxPriceSlider.value);

        // Update slider track color
        updateSliderTrack();
    }

    // Update slider track visual
    function updateSliderTrack() {
        const minVal = parseInt(minPriceSlider.value);
        const maxVal = parseInt(maxPriceSlider.value);
        const minRange = parseInt(minPriceSlider.min);
        const maxRange = parseInt(minPriceSlider.max);

        const minPercent = ((minVal - minRange) / (maxRange - minRange)) * 100;
        const maxPercent = ((maxVal - minRange) / (maxRange - minRange)) * 100;

        // Create visual track effect
        const rangeSlider = document.querySelector('.range-slider');
        let track = rangeSlider.querySelector('.slider-track');

        if (!track) {
            track = document.createElement('div');
            track.className = 'slider-track';
            track.style.position = 'absolute';
            track.style.height = '6px';
            track.style.background = '#ff6b6b';
            track.style.borderRadius = '3px';
            track.style.top = '17px';
            track.style.zIndex = '1';
            rangeSlider.appendChild(track);
        }

        track.style.left = minPercent + '%';
        track.style.width = (maxPercent - minPercent) + '%';
    }

    // Reset all filters
    function resetFilters() {
        // Reset price sliders
        minPriceSlider.value = minPriceSlider.min;
        maxPriceSlider.value = maxPriceSlider.max;
        updatePriceDisplay();

        // Reset all checkboxes
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    // Apply filters
    function applyFilters() {
        const filters = {
            minPrice: parseInt(minPriceSlider.value),
            maxPrice: parseInt(maxPriceSlider.value),
            features: [],
            memory: []
        };

        // Get selected features
        const featureCheckboxes = document.querySelectorAll('.filter-section:nth-child(2) input[type="checkbox"]:checked');
        featureCheckboxes.forEach(checkbox => {
            filters.features.push(checkbox.value);
        });

        // Get selected memory options
        const memoryCheckboxes = document.querySelectorAll('.filter-section:nth-child(3) input[type="checkbox"]:checked');
        memoryCheckboxes.forEach(checkbox => {
            filters.memory.push(checkbox.value);
        });

        console.log('Applied filters:', filters);

        // Here you would typically send the filters to your backend or update the product display
        // For demo purposes, we'll just show an alert
        let filterSummary = `Bộ lọc đã áp dụng:\n`;
        filterSummary += `Giá: ${formatPrice(filters.minPrice)} - ${formatPrice(filters.maxPrice)}\n`;

        if (filters.features.length > 0) {
            filterSummary += `Tính năng: ${filters.features.join(', ')}\n`;
        }

        if (filters.memory.length > 0) {
            filterSummary += `Bộ nhớ: ${filters.memory.join(', ')}\n`;
        }

        alert(filterSummary);
        closePopup();
    }

    // Event listeners
    filterBtn.addEventListener('click', openPopup);
    closeBtn.addEventListener('click', closePopup);
    overlay.addEventListener('click', closePopup);
    resetBtn.addEventListener('click', resetFilters);
    applyBtn.addEventListener('click', applyFilters);

    // Price slider event listeners
    minPriceSlider.addEventListener('input', updatePriceDisplay);
    maxPriceSlider.addEventListener('input', updatePriceDisplay);

    // Close popup with Escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape' && filterPopup.style.display === 'block') {
            closePopup();
        }
    });

    // Initialize price display
    updatePriceDisplay();

    // Handle desktop filter buttons (optional - you can expand this)
    const desktopFilterBtns = document.querySelectorAll('.desktop-filters .filter-btn');
    desktopFilterBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            // For now, just open the main popup
            // You could create separate popups for each filter type
            openPopup();
        });
    });

    // Add hover effects for desktop filter buttons
    desktopFilterBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(180deg)';
            }
        });

        btn.addEventListener('mouseleave', function () {
            const arrow = this.querySelector('.arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0deg)';
            }
        });
    });
});
